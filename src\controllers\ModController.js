const Request = require('request');
const Fs = require('fs-extra');
const {exec} = require('child_process');

const ConfigHelper = require('./../helpers/config');
const ResponseHelper = require('./../helpers/responses');
const Path = require('path');
const compressing = require('compressing');

const Config = new ConfigHelper();

class ModController {
    constructor(auth, req, res) {
        this.req = req;
        this.res = res;

        this.auth = auth;
        this.responses = new ResponseHelper(req, res);
    }

    installMod() {
        this.auth.allowed('s:mods', (allowedErr, isAllowed) => {
            if (allowedErr || !isAllowed) return;

            if ("url" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing url argument"});
            if ("folder" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing folder argument"});
            if ("decompress_type" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing decompress_type argument"});

            const url = this.req.params["url"];
            const folder = this.req.params["folder"];
            const decompress_type = this.req.params["decompress_type"];

            let fileName = 'default_filename';

            const uuid = this.auth.server().uuid;
            const server = this.auth.server();
            const res = this.res;

            if (decompress_type == 'unzip') {
                fileName = 'mod.zip';
            } else {
                fileName = url.split('/').pop();
            }

            const sftpPath = Config.get('sftp.path', '/srv/daemon-data')
            const current_path = Path.join(sftpPath, uuid, folder);
            Fs.access(current_path, (error) => {
                if (error) {
                    this.auth.server().fs.mkdir(folder, (err) => {
                        if (err) {
                            return res.send({"success": "false", "error": "Download folder not found!"});
                        }
                    });
                }

                const file = Fs.createWriteStream(Path.join(current_path, fileName));

                const options = {
                    url: url,
                    headers: { 'User-Agent': 'Mozilla/5.0' }
                };

                Request.get(options)
                    .pipe(file)
                    .on('finish', function () {

                        if (decompress_type == 'unzip') {

                            compressing.zip.uncompress(Path.join(sftpPath, uuid, folder, 'mod.zip'), Path.join(sftpPath, uuid, folder))
                                .then(() => {
                                    return Fs.remove(Path.join(sftpPath, uuid, folder, 'mod.zip'))
                                }).then( () => {
                                return res.send({"success": "true"});

                            })
                                .catch(e => {
                                    return res.send({"success": "false", "error": "Unzip mod error!"});

                                });



                        } else {
                            return res.send({"success": "true"});
                        }

                    })
                    .on('error', function (err) {
                        return res.send({"success": "false", "error": "Download mod error!"});
                    });
            });
        });
    }

    uninstallMod() {
        this.auth.allowed('s:mods', (allowedErr, isAllowed) => {
            if (allowedErr || !isAllowed) return;

            if ("base" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing base argument"});
            if ("removes" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing removes argument"});
            if ("remove_base" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing remove_base argument"});

            let error = false;

            const res = this.res;
            const uuid = this.auth.server().uuid;
            const base = this.req.params["base"];
            const removes = JSON.parse(this.req.params["removes"]);
            const remove_base = this.req.params["remove_base"];

            if (remove_base == 1) {
              //TODO
            } else {
                for (let r in removes) {
                    Fs.remove(Path.join(Config.get('sftp.path').toString(),uuid,removes[r]), (err) => {
                        if (err) {
                            error = true;
                        }
                    });
                }
            }

            if (error) {
                return res.send({"success": "false", "error": "File delete error!"});
            }

            return res.send({"success": "true"});
        });
    }

    responseToPanel(uri, params) {
        Request(`${Config.get('remote.base')}/api/remote/${uri}`, {
            method: 'POST',
            json: params,
            headers: {
                'Accept': 'application/vnd.pterodactyl.v1+json',
                'Authorization': `Bearer ${Config.get('keys.0')}`,
            },
            timeout: 5000,
        }, (err, response, body) => {
            if (err) {
                return false;
            }

            return response;
        });
    }
}

module.exports = ModController;
