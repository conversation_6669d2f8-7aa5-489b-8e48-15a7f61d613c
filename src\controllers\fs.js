'use strict';

const Fs = require('fs-extra');

const getSize = require('get-folder-size');
const languageEncoding = require("detect-file-encoding-and-language");
const {sync: mime, async: mimeAsync} = require('mime-kind');

const Async = require('async');
const Path = require('path');
const Chokidar = require('chokidar');
const _ = require('lodash');
const compressing = require('compressing');


const RandomString = require('randomstring');
const Process = require('child_process');
const Util = require('util');
const rfr = require('rfr');
const iconvlite = require('iconv-lite');
const fs = require("fs");


const ConfigHelper = rfr('src/helpers/config.js');
const Config = new ConfigHelper();

class FileSystem {
    constructor(server) {
        this.server = server;

        const Watcher = Chokidar.watch(this.server.configLocation, {
            persistent: true,
            awaitWriteFinish: false,
        });

        Watcher.on('change', () => {
            if (this.server.knownWrite !== true) {
                this.server.log.debug('Detected remote file change, updating JSON object correspondingly.');
                Fs.readJson(this.server.configLocation, (err, object) => {
                    if (err) {
                        // Try to overwrite those changes with the old config.
                        this.server.log.warn(err, 'An error was detected with the changed file, attempting to undo the changes.');
                        this.server.knownWrite = true;
                        Fs.writeJson(this.server.configLocation, this.server.json, {spaces: 2}, writeErr => {
                            if (!writeErr) {
                                this.server.log.debug('Successfully undid those remote changes.');
                            } else {
                                this.server.log.fatal(writeErr, 'Unable to undo those changes, this could break the daemon badly.');
                            }
                        });
                    } else {
                        this.server.json = object;
                    }
                });
            }
            this.server.knownWrite = false;
        });
    }

    file_encoding(file, encoding) {
        let content = Fs.readFileSync(file);
        return iconvlite.decode(content, encoding)
    }


    size(next) {


        getSize(this.server.path(), function (err, size) {
            if (err) {
                this.server.log.error(err);
                return next(new Error('There was an error while attempting to check the size of the server data folder.'));
            }
            return next(null, size);

        });


    }

    chown(file, next) {
        return next();
    }

    isSelf(moveTo, moveFrom) {
        const target = this.server.path(moveTo);
        const source = this.server.path(moveFrom);

        if (!_.startsWith(target, source)) {
            return false;
        }

        const end = target.slice(source.length);
        if (!end) {
            return true;
        }

        return _.startsWith(end, '/');
    }


    write(file, data, next) {
        Async.series([
            callback => {
                Fs.ensureFile(this.server.path(file), err => {
                    if (err) return callback(err)
                    callback()
                })
            },
            callback => {
                languageEncoding(this.server.path(file)).then(fileInfo => {
                    Fs.outputFile(this.server.path(file), data, {encoding: fileInfo.encoding}, callback);
                }).catch(e => {
                    Fs.outputFile(this.server.path(file), data, {encoding: 'utf8'}, callback);
                })

            },
            callback => {
                this.chown(file, callback);
            },
        ], next);
    }


    read(file, next) {
        Fs.stat(this.server.path(file), (err, stat) => {
            if (err) return next(err);
            if (!stat.isFile()) {
                return next(new Error('The file requested does not appear to be a file.'));
            }

            if (stat.size > 10000000) {
                return next(new Error('This file is too large to open.'));
            }

            languageEncoding(this.server.path(file)).then(charsetMatch => {
                Fs.readFile(this.server.path(file), {encoding: charsetMatch.encoding}, next);
            }).catch(e => {
                if (e instanceof TypeError) {
                    Fs.readFile(this.server.path(file), {encoding: 'utf8'}, next);
                }
            })

        });
    }

    readBytes(file, offset, length, next) {
        Fs.stat(this.server.path(file), (err, stat) => {
            if (err) return next(err);
            if (!stat.isFile()) {
                const internalError = new Error('Trying to read bytes from a non-file.');
                internalError.code = 'EISDIR';

                return next(internalError);
            }

            if (offset >= stat.size) {
                return next(null, null, true);
            }

            const chunks = [];
            const stream = Fs.createReadStream(this.server.path(file), {
                start: offset,
                end: (offset + length) - 1,
            });
            stream.on('data', data => {
                chunks.push(data);
            });
            stream.on('end', () => {
                next(null, Buffer.concat(chunks), false);
            });
        });
    }

    readEnd(file, bytes, next) {
        if (_.isFunction(bytes)) {
            next = bytes; // eslint-disable-line
            bytes = 80000; // eslint-disable-line
        }
        Fs.stat(this.server.path(file), (err, stat) => {
            if (err) return next(err);
            if (!stat.isFile()) {
                return next(new Error('The file requested does not appear to be a file.'));
            }
            let opts = {};
            let lines = '';
            if (stat.size > bytes) {
                opts = {
                    start: (stat.size - bytes),
                    end: stat.size,
                };
            }
            const stream = Fs.createReadStream(this.server.path(file), opts);
            stream.on('data', data => {
                lines += data;
            });
            stream.on('end', () => {
                next(null, lines);
            });
        });
    }

    mkdir(path, next) {
        if (!_.isArray(path)) {
            Fs.ensureDir(this.server.path(path), err => {
                if (err) return next(err);
                this.chown(path, next);
            });
        } else {
            Async.eachOfLimit(path, 5, (value, key, callback) => {
                Fs.ensureDir(this.server.path(value), err => {
                    if (err) return callback(err);
                    this.chown(value, callback);
                });
            }, next);
        }
    }

    rm(path, next) {
        if (_.isString(path)) {
            // Safety - prevent deleting the main folder.
            if (Path.resolve(this.server.path(path)) === this.server.path()) {
                return next(new Error('You cannot delete your home folder.'));
            }

            Fs.remove(this.server.path(path), next);
        } else {
            Async.eachOfLimit(path, 5, (value, key, callback) => {
                // Safety - prevent deleting the main folder.
                if (Path.resolve(this.server.path(value)) === this.server.path()) {
                    return next(new Error('You cannot delete your home folder.'));
                }

                Fs.remove(this.server.path(value), callback);
            }, next);
        }
    }

    copy(initial, ending, opts, next) {
        if (_.isFunction(opts)) {
            next = opts; // eslint-disable-line
            opts = {}; // eslint-disable-line
        }

        if (!_.isArray(initial) && !_.isArray(ending)) {
            if (this.isSelf(ending, initial)) {
                return next(new Error('You cannot copy a folder into itself.'));
            }
            Async.series([
                callback => {
                    Fs.copy(this.server.path(initial), this.server.path(ending), {
                        overwrite: opts.overwrite || true,
                        preserveTimestamps: opts.timestamps || false,
                    }, callback);
                },
                callback => {
                    this.chown(ending, callback);
                },
            ], next);
        } else if (!_.isArray(initial) || !_.isArray(ending)) {
            return next(new Error('Values passed to copy function must be of the same type (string, string) or (array, array).'));
        } else {
            Async.eachOfLimit(initial, 5, (value, key, callback) => {
                if (_.isUndefined(ending[key])) {
                    return callback(new Error('The number of starting values does not match the number of ending values.'));
                }

                if (this.isSelf(ending[key], value)) {
                    return next(new Error('You cannot copy a folder into itself.'));
                }
                Fs.copy(this.server.path(value), this.server.path(ending[key]), {
                    overwrite: _.get(opts, 'overwrite', true),
                    preserveTimestamps: _.get(opts, 'timestamps', false),
                }, err => {
                    if (err) return callback(err);
                    this.chown(ending[key], callback);
                });
            }, next);
        }
    }

    stat(file, next) {
        Fs.lstat(this.server.path(file), (err, stat) => {
            if (err) return next(err);
            let result
            if (this.server.path(file).indexOf('.cfg') > -1) {
                result = 'text/plain'
            }else {
                result = mime(this.server.path(file)).mime
            }
            next(null, {
                'name': (Path.parse(this.server.path(file))).base,
                'created': stat.birthtime,
                'modified': stat.mtime,
                'mode': stat.mode,
                'size': stat.size,
                'directory': stat.isDirectory(),
                'file': stat.isFile(),
                'symlink': stat.isSymbolicLink(),
                'mime': result || 'application/octet-stream',
            });
        });

    }

    move(initial, ending, next) {
        if (!_.isArray(initial) && !_.isArray(ending)) {
            if (this.isSelf(ending, initial)) {
                return next(new Error('You cannot move a file or folder into itself.'));
            }
            Fs.move(this.server.path(initial), this.server.path(ending), {overwrite: false}, err => {
                if (err && !_.startsWith(err.message, 'EEXIST:')) return next(err);
                this.chown(ending, next);
            });
        } else if (!_.isArray(initial) || !_.isArray(ending)) {
            return next(new Error('Values passed to move function must be of the same type (string, string) or (array, array).'));
        } else {
            Async.eachOfLimit(initial, 5, (value, key, callback) => {
                if (_.isUndefined(ending[key])) {
                    return callback(new Error('The number of starting values does not match the number of ending values.'));
                }

                if (this.isSelf(ending[key], value)) {
                    return next(new Error('You cannot move a file or folder into itself.'));
                }
                Fs.move(this.server.path(value), this.server.path(ending[key]), {overwrite: false}, err => {
                    if (err && !_.startsWith(err.message, 'EEXIST:')) return callback(err);
                    this.chown(ending[key], callback);
                });
            }, next);
        }
    }

    decompress(files, next) {

        if (!_.isArray(files)) {
            const fromFile = this.server.path(files);
            const toDir = Path.dirname(fromFile)
            this.systemDecompress(fromFile, toDir, next);
        } else if (_.isArray(files)) {
            Async.eachLimit(files, 1, (file, callback) => {
                const fromFile = this.server.path(file);
                const toDir = Path.dirname(fromFile)
                this.systemDecompress(fromFile, toDir, callback);
            }, next);
        } else {
            return next(new Error('Invalid datatype passed to decompression function.'));
        }
    }

    systemDecompress(file, to, next) {
        const dangerExtensions = Config.get('filesystem.danger_extensions')
        new compressing.zip.UncompressStream({source: file})
            .on('error', (err) => next(err))
            .on('finish', () => next()) // uncompressing is done
            .on('entry', (header, stream, n) => {
                stream.on('end', n);
                const dest = Path.join(to, header.name)
                if (header.type === 'file') {
                    if (dangerExtensions.indexOf(Path.parse(header.name).ext) === -1) {
                        //no found danger file
                        Fs.ensureFileSync(dest)
                        const file = fs.createWriteStream(dest)
                        file.on('error', function (err) {
                            file.end();
                            return n(err)
                        });
                        stream.pipe(file);
                    }else {
                        stream.resume();
                    }
                } else { // directory
                    Fs.ensureDirSync(dest)
                    stream.resume();
                }
            })
    }

    // Unlike other functions, if multiple files and folders are passed
    // they will all be combined into a single archive.
    compress(files, to, next) {
        if (!_.isString(to)) {
            return next(new Error('The to field must be a string for the folder in which the file should be saved.'));
        }


        let saveAsName = `pterodactyl.archive.${RandomString.generate(4)}.zip`;
        if (this.isSelf(to, files)) {
            return next(new Error('Unable to compress folder into itself.'));
        }

        saveAsName = `${Path.basename(this.server.path(files), Path.extname(files))}.${RandomString.generate(4)}.zip`;
        compressing.zip.compressDir(this.server.path(files), this.server.path(saveAsName)).then(compressDone => {
            return next(null, Path.basename(saveAsName));
        }).catch(e => {
            return next(e);
        })


    }


    directory(path, next) {
        const responseFiles = [];

        Async.waterfall([
            callback => {
                Fs.stat(this.server.path(path), (err, s) => {
                    if (err) return callback(err);
                    if (!s.isDirectory()) {
                        const error = new Error('The path requests is not a valid directory on the system.');
                        error.code = 'ENOENT';
                        return callback(error);
                    }
                    return callback();
                });
            },
            callback => {
                Fs.readdir(this.server.path(path), callback);
            },
            (files, callback) => {
                Async.each(files, (item, eachCallback) => {
                    Async.auto({
                        do_stat: aCallback => {
                            Fs.stat(Path.join(this.server.path(path), item), (statErr, stat) => {
                                // Handle bad symlinks
                                if (statErr && statErr.code === 'ENOENT') {
                                    return eachCallback();
                                }
                                aCallback(statErr, stat);
                            });
                        },
                        do_realpath: aCallback => {
                            Fs.realpath(Path.join(this.server.path(path), item), (rpErr, realPath) => {
                                aCallback(null, realPath);
                            });
                        },
                        do_mime: aCallback => {
                            const a = Path.join(this.server.path(path), item)
                            Fs.stat(a, (err, stats) => {
                                if (err) return callback(err)
                                if (stats.isFile()) {
                                    const result = mime(a)
                                    if (_.get(result, 'mime')) {
                                        aCallback(null, result.mime);
                                    } else {

                                        if (a.indexOf('.cfg') > -1) {
                                            aCallback(null, 'text/plain')
                                        } else {
                                            aCallback(null);
                                        }

                                    }
                                } else {
                                    aCallback(null)
                                }
                            })
                        },
                        do_push: ['do_stat', 'do_mime', 'do_realpath', (results, aCallback) => {
                            if (!_.startsWith(results.do_realpath, this.server.path())) {
                                return aCallback();
                            }
                            responseFiles.push({
                                'name': item,
                                'created': results.do_stat.birthtime,
                                'modified': results.do_stat.mtime,
                                'mode': results.do_stat.mode,
                                'size': results.do_stat.size,
                                'directory': results.do_stat.isDirectory(),
                                'file': results.do_stat.isFile(),
                                'symlink': results.do_stat.isSymbolicLink(),
                                'mime': results.do_mime || 'application/octet-stream'

                            });
                            aCallback();
                        }],
                    }, eachCallback);
                }, callback);
            },
        ], err => {
            next(err, _.sortBy(responseFiles, [(o) => {
                return _.lowerCase(o.name);
            }, 'created'])); // eslint-disable-line
        });
    }
}

module.exports = FileSystem;
