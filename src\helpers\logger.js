'use strict';
const Bunyan = require('bunyan');
const Path = require('path');
const rfr = require('rfr')
const LoadConfig = rfr('src/helpers/config.js');
const Config = new LoadConfig();

const Log = Bunyan.createLogger({
    name: 'wings',
    src: Config.get('logger.src', false),
    serializers: Bunyan.stdSerializers,
    streams: [
        {
            level: Config.get('logger.level', 'info'),
            stream: process.stdout,
        },
        {
            type: 'rotating-file',
            level: Config.get('logger.level', 'info'),
            path: Path.join(Config.get('logger.path', 'logs/'), 'wings.log'),
            period: Config.get('logger.period', '1d'),
            count: Config.get('logger.count', 3),
        },
    ],
});

module.exports = Log;
