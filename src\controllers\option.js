'use strict';
const rfr = require('rfr');
const Async = require('async');
const Request = require('request');
const Fs = require('fs-extra');
const Path = require('path');
const Util = require('util');
const _ = require('lodash');
const ScriptLang = rfr('src/helpers/scripts.js')
const ConfigHelper = rfr('src/helpers/config.js');
const nssm = require('windows-service-wrapper').Service;


const Config = new ConfigHelper();
const Script = new ScriptLang();

class Option {
    constructor(server) {
        this.server = server;
        this.processLogger = undefined;
    }

    pull(next) {
        this.server.log.debug('Contacting panel to determine scripts to run for option processes.');

        //测试修改为 31bcdd48-6f84-4ed9-88ab-365e94692267
        // const endpoint = `${Config.get('remote.base')}/api/remote/scripts/${this.server.json.uuid}`;
        const endpoint = `${Config.get('remote.base')}/api/remote/scripts/31bcdd48-6f84-4ed9-88ab-365e94692267`;

        Request({
            method: 'GET',
            url: endpoint,
            timeout: 5000,
            headers: {
                'Accept': 'application/vnd.pterodactyl.v1+json',
                'Authorization': `Bearer ${Config.get('keys.0')}`,
            },
        }, (err, resp) => {
            if (err) return next(err);
            if (resp.statusCode !== 200) {
                const error = new Error('Recieved a non-200 error code when attempting to check scripts for server.');
                error.meta = {
                    code: resp.statusCode,
                    requestUrl: endpoint,
                };
                return next(error);
            }

            const Results = JSON.parse(resp.body);
            return next(null, Results);
        });
    }

    install(next) {
        this.server.log.info('Blocking server boot until option installation process is completed.');
        this.server.blockBooting = true;

        const serverPath = Config.get("sftp.path");
        Async.auto({
            details: callback => {
                this.server.log.debug('Contacting remote server to pull scripts to be used.');
                this.pull(callback);
            },
            struct_check: ['details', (results, callback) => {
                if (_.isNil(_.get(results.details, 'scripts.install', null))) {
                    // No script defined, skip the rest.
                    const error = new Error('No installation script was defined for this egg, skipping rest of process.');
                    error.code = 'E_NOSCRIPT';
                    return callback(error);
                }

                this.server.log.debug('Checking file struct ...');
                return callback(null, results)

            }],

            run: ['struct_check', (results, callback) => {
                this.server.log.debug('Running privileged docker container to perform the installation process.');

                _.forEach(_.get(results.details.scripts.install, 'install', []), (v, k) => {
                    Script.run(this.server.json.uuid, v)
                })

                return callback(null, results)

            }],

            install_windows_service: ['run', (results, callback) => {
                this.server.log.info('Prepare to install windows service.');


                const wrapper = new nssm();
                wrapper.install({
                    serviceName: this.server.json.uuid,
                    executeFile: Path.join(serverPath, this.server.json.uuid, this.server.json.service.exe_path),
                    parameter: this.server.json.service.startup_params,

                }).then(() => {
                    return wrapper.set(this.server.json.uuid, {AppRestartDelay: 2000, Start: 'SERVICE_DEMAND_START'})
                }).then(() => {
                    callback(null, `Server ${this.server.json.uuid} install Service success`)
                }).catch(err => {
                    callback(err)
                })

            }]

        }, err => {

            console.log(err)
            // No script, no need to kill everything.
            if (err && err.code === 'E_NOSCRIPT') {
                this.server.log.info(err.message);
                return next();
            }

            return next(err);
        });
    }
}

module.exports = Option;
