const Fs = require('fs-extra');
const SteamWorkshop = require('steam-workshop');
const simpleGit = require('simple-git');
const {exec} = require('child_process');
const Path = require('path');
const _ = require('lodash')

const ConfigHelper = require('./../helpers/config');
const ResponseHelper = require('./../helpers/responses');

const SteamCMD = require('../steamDownloader/steamcmd');

const Config = new ConfigHelper();

class GitSteamController {
    constructor(auth, req, res) {
        this.req = req;
        this.res = res;

        this.auth = auth;
        this.responses = new ResponseHelper(req, res);
    }

    steamWorkshop() {
        this.auth.allowed('s:downloader:steam', (allowedErr, isAllowed) => {
            if (allowedErr || !isAllowed) return;

            if ("path" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing path argument."});
            if ("workshop" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing workshop argument."});

            const path = this.req.params["path"];
            let workshop = this.req.params["workshop"];

            workshop = workshop.split('=');
            workshop[1] ? workshop = workshop[1] : workshop = workshop[0];

            const uuid = this.auth.server().uuid;
            const res = this.res;

            Fs.access(Config.get('sftp.path').toString() + '/' + uuid + path, (error) => {
                if (!error) {
                    let steamWorkshop = new SteamWorkshop(Config.get('sftp.path').toString() + '/' + uuid + path);

                    steamWorkshop.getPublishedFileDetails(workshop, (err, files) => {
                        if (err) {

                            return res.send({"success": "false", "error": "Workshop item not found."});
                        } else {

                            console.log(files[0])

                            if (!this.auth.server().json['build']['env']['SRCDS_GAME_APPID']) {
                                return res.send({
                                    "success": "false",
                                    "error": "Failed to find AppID. 请重试"
                                });
                            }

                            let env = this.auth.server().json['build']['env'];
                            let opts = {
                                applicationId: env['SRCDS_GAME_APPID'], //需要下载MOD的游戏ID
                                workshopId: workshop,
                                path: Config.get('sftp.path').toString() + '/' + uuid + '/'
                            };


                            if ('STEAM_USERNAME' in env) {
                                opts['username'] = env['STEAM_USERNAME'];
                            }

                            if ('STEAM_PASSWORD' in env) {
                                opts['password'] = env['STEAM_PASSWORD'];
                            }

                            SteamCMD.install(opts, (err) => {
                                if (err == null) {
                                    let game_id = this.auth.server().json['build']['env']['SRCDS_GAME_APPID']
                                    let src_path = Path.join(Config.get('sftp.path').toString(), uuid, '/steamapps/workshop/content/', game_id, workshop);
                                    let mod_dir_name = _.get(files[0], 'title', workshop)
                                    if (game_id === '221100') {
                                        mod_dir_name = `@${mod_dir_name}`
                                    }
                                    let dest_path = Path.join(Config.get('sftp.path').toString(), uuid, path, mod_dir_name)

                                    Fs.copy(src_path, dest_path, err => {
                                        if (err) {
                                            return res.send({
                                                "success": "false",
                                                "error": "Copy Error: Please try again later..."
                                            });
                                        }

                                        this.auth.server().fs.rm('/steamapps/workshop/', (err) => {

                                        });

                                        return res.send({"success": "true", "title": mod_dir_name});
                                    });

                                } else {
                                    if (err instanceof Error && err.includes('steamcmd failed')) {
                                        this.auth.server().fs.rm('/steamapps/workshop/', (err) => {
                                        });

                                        return res.send({
                                            "success": "false",
                                            "error": "Failed to download workshop content."
                                        });
                                    }
                                }
                            });
                        }
                    });
                } else {
                    return res.send({"success": "false", "error": "Path not found."});
                }
            });
        });
    }

    gitClone() {
        this.auth.allowed('s:downloader:git', (allowedErr, isAllowed) => {
            if (allowedErr || !isAllowed) return;

            if ("path" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing path argument."});
            if ("git" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing git argument."});
            if ("branch" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing branch argument."});

            let path = this.req.params["path"];
            let git = this.req.params["git"];
            let branch = this.req.params["branch"];

            if (branch === '') {
                branch = 'master';
            }

            const uuid = this.auth.server().uuid;
            const res = this.res;

            Fs.access(Config.get('sftp.path').toString() + '/' + uuid + path, error => {
                if (!error) {
                    simpleGit(Config.get('sftp.path').toString() + '/' + uuid + path).clone(git, ['--single-branch', '--branch', branch], (err, result) => {
                        if (err) {
                            return res.send({"success": "false", "error": "Failed to clone this repository."});
                        }

                        return res.send({"success": "true"});
                    });
                } else {
                    return res.send({"success": "false", "error": "Path not found."});
                }
            });
        });
    }

    gitPull() {
        this.auth.allowed('s:downloader:git', (allowedErr, isAllowed) => {
            if (allowedErr || !isAllowed) return;

            if ("path" in this.req.params === false)
                return this.res.send({"success": "false", "error": "Missing path argument."});

            let path = this.req.params["path"];

            const uuid = this.auth.server().uuid;
            const res = this.res;

            Fs.access(Config.get('sftp.path').toString() + '/' + uuid + path + '.git', error => {
                if (!error) {
                    simpleGit(Config.get('sftp.path').toString() + '/' + uuid + path).pull((err, update) => {
                        if (err) {
                            return res.send({"success": "false", "error": "Failed to pull this repository."});
                        } else {
                            return res.send({"success": "true"});
                        }
                    });
                } else {
                    return res.send({"success": "false", "error": "Git file or path not found."});
                }
            });
        });
    }
}

module.exports = GitSteamController;
