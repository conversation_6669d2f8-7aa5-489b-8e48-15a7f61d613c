const rfr = require('rfr');
const Fs = require('fs-extra');
const Restify = require('restify');
const Bunyan = require('bunyan');
const Path = require('path');

const ConfigHelper = rfr('src/helpers/config.js');
const Config = new ConfigHelper();

const RestLogger = Bunyan.createLogger({
    name: 'restify.logger',
    serializers: Bunyan.stdSerializers,
    streams: [
        {
            level: 'info',
            type: 'rotating-file',
            path: Path.join(Config.get('logger.path', 'logs/'), 'request.log'),
            period: '4h',
            count: 3,
        },
    ],
});

const RestServer = Restify.createServer({
    name: 'Pterodactyl Daemon',
    certificate: (Config.get('web.ssl.enabled') === true) ? Fs.readFileSync(Config.get('web.ssl.certificate')) : null,
    key: (Config.get('web.ssl.enabled') === true) ? Fs.readFileSync(Config.get('web.ssl.key')) : null,
    formatters: {
        'application/json': (req, res, body, callback) => {
            callback(null, JSON.stringify(body, null, 4));
        },
    },
});

RestServer.pre((req, res, next) => {
    // Fix Headers
    if ('x-access-server' in req.headers && !('X-Access-Server' in req.headers)) {
        req.headers['X-Access-Server'] = req.headers['x-access-server']; // eslint-disable-line
    }

    if ('x-access-token' in req.headers && !('X-Access-Token' in req.headers)) {
        req.headers['X-Access-Token'] = req.headers['x-access-token']; // eslint-disable-line
    }
    return next();
});

RestServer.on('after', Restify.auditLogger({
    log: RestLogger,
}));

// Export this for Socket.io to make use of.
module.exports = RestServer;
