const http = require('http');
const os = require('os');
const path = require('path-extra');
const tarball = require('tarball-extract');
const unzip = require('unzipper');

const env = require('./env');

const urls = {
    darwin: 'http://media.steampowered.com/client/installer/steamcmd_osx.tar.gz',
    linux: 'http://media.steampowered.com/installer/steamcmd_linux.tar.gz',
    win32: 'http://media.steampowered.com/installer/steamcmd.zip'
};

function download(callback) {
    let platform = os.platform();
    let url = urls[platform];

    if (url) {
        if (platform === 'darwin' || platform === 'linux') {
            let tempFile = path.resolve(path.tempdir(), 'steamcmd.tar.gz');

            tarball.extractTarballDownload(url, tempFile, env.directory(), {}, function (err, result) {
                callback(err);
            })
        } else if (platform === 'win32') {
            http.get(url, function (res) {
                res.pipe(unzip.Extract({path: env.directory()}))
                    .on('error', function (err) {
                        callback(err);
                    })
                    .on('close', function () {
                        callback(null);
                    });
            });
        }
    } else {
        callback(new Error('Platform unsupported'));
    }
}

module.exports = download;
