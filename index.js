'use strict';

const https = require('https');
const rfr = require('rfr');
const Async = require('async');
const Proc = require('child_process');
const Request = require('request');
const compareVersions = require('compare-versions');
const Fs = require('fs-extra');
const _ = require('lodash');
const Keypair = require('keypair');
const Getos = require('getos');
const Path = require("path");
const FtpSrv = require('ftp-srv-u');
const GameServiceController = rfr('src/controllers/GameService.js');

const Log = rfr('src/helpers/logger.js');
const Package = rfr('package.json');
const ConfigHelper = rfr('src/helpers/config.js');
const Config = new ConfigHelper();
const Service = new GameServiceController();



Log.info('+ ------------------------------------ +');
Log.info(`| Running Pterodactyl Daemon v${Package.version}    |`);
Log.info('|        https://pterodactyl.io        |');
Log.info('|  Copyright 2015 - 2019 Dane Everitt  |');
Log.info('+ ------------------------------------ +');
Log.info('Loading modules, this could take a few seconds.');

const LiveStats = rfr('src/http/stats.js');

const Stats = new LiveStats();

// const NetworkController = rfr('src/controllers/network.js');
const Initializer = rfr('src/helpers/initialize.js').Initialize;
// const ServiceController = rfr('src/controllers/service.js');
// const SftpServer = rfr('src/http/sftp.js');
//
// const Network = new NetworkController();
const Initialize = new Initializer();
// const Service = new ServiceController();
// const Sftp = new SftpServer();

const userDefinedCAStores = Config.get('internals.ca_stores', []);
if (userDefinedCAStores.length > 0) {
    Log.info('Found user provided CA store settings, synchronously applying those now...');

    https.globalAgent.options.ca = [];
    _.forEach(userDefinedCAStores, store => {
        https.globalAgent.options.ca.push(Fs.readFileSync(store));
    });
}

Log.info('Modules loaded, starting Pterodactyl Daemon...');
Async.auto({
    check_structure: callback => {
        Fs.ensureDirSync('config/servers');
        Fs.ensureDirSync(Config.get('filesystem.server_logs', 'config/pterodactyl'));

        callback();
    },


    setup_ftp_server: ['check_structure', (r, callback)  => {
        const ftpServer = new FtpSrv({
            url: `ftp://${Config.get('sftp.ip', '0.0.0.0')}:${Config.get('sftp.port', 2022)}`,
            greeting: 'Welcome v2pg.com FTP Server',
            deny_extension: ['exe', 'dll', 'bat', 'cmd', 'vbs', 'inf', 'vbe', 'vbs', 'com', 'ws', 'reg'],
        });


        ftpServer.listen().then(() => {
            console.log('FTP Server has been started.')
            return callback()
        });

        ftpServer.on('login', async ({connection, username, password}, resolve, reject) => {

            const endpoint = `${Config.get('remote.base')}/api/remote/sftp`;
            const basePath = Config.get('sftp.path');
            try {
                const result = Request.get(endpoint, {
                    dataType: 'json',
                    method: 'POST',
                    contentType: 'json',
                    data: {
                        username: username,
                        password: password
                    },
                    headers: {
                        'Authorization': `Bearer ${this.app.config.key}`,
                    },

                });
                Log.info('ftp auth result : %s', JSON.stringify(result.data))
                if (result.data.error) {
                    reject('用户名或密码错误，请根据 后台文件传输 页面提示使用此功能.')
                }
                resolve({
                    root: Path.join(basePath, result.data.server)
                });

            }catch (e) {
                Log.error('ftp auth exception: %s',  e)
                reject(e);
            }

        });
    }],


    check_services: ['setup_ftp_server', (r, callback) => {
        Service.boot(callback);
    }],


    init_servers: ['check_services', (r, callback) => {
        Log.info('Beginning server initialization process.');
        Initialize.init(callback);
    }],

    init_websocket: ['init_servers', (r, callback) => {
        Log.info('Configuring websocket for daemon stats...');
        Stats.init();
        return callback();
    }],
}, (err, results) => {
    if (err) {
        // Log a fatal error and exit.
        // We need this to initialize successfully without any errors.
        Log.fatal({ err, additional: err }, 'A fatal error caused the daemon to abort the startup.');
        if (err.code === 'ECONNREFUSED') {
            Log.fatal('+ ------------------------------------ +');
            Log.fatal('|  Docker is not running!              |');
            Log.fatal('|                                      |');
            Log.fatal('|  Unable to locate a suitable socket  |');
            Log.fatal('|  at path specified in configuration. |');
            Log.fatal('+ ------------------------------------ +');
        }

        Log.error('You should forcibly quit this process (CTRL+C) and attempt to fix the issue.');
    } else {
        rfr('src/http/routes.js');
    }
});

process.on('uncaughtException', err => {
    Log.fatal(err, 'A fatal error occured during an operation.');
});

process.on('SIGUSR2', () => {
    Log.reopenFileStreams();
});
