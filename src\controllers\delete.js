'use strict';
const rfr = require('rfr');
const Async = require('async');
const Fs = require('fs-extra');
const Path = require('path');
const _ = require('lodash');

const ConfigHelper = rfr('src/helpers/config.js');
const Log = rfr('src/helpers/logger.js');
const Status = rfr('src/helpers/status.js');
const Nssm = require('windows-service-wrapper').Service;
const Config = new ConfigHelper();


class Delete {
    constructor(json) {
        this.json = json;
        this.log = Log.child({ server: this.json.uuid });
    }

    delete(next) {
        Async.auto({
            // Clear the 'Servers' object of the specific server
            clear_object: callback => {
                this.log.debug('Clearing servers object...');
                const Servers = rfr('src/helpers/initialize.js').Servers;

                // Prevent crash detection
                if (!_.isUndefined(Servers[this.json.uuid]) && _.isFunction(Servers[this.json.uuid].setStatus)) {
                    const server = Servers[this.json.uuid];

                    clearInterval(server.intervals.diskUse);

                    if (!_.isNil(server.container)) {

                        if (!_.isNil(server.container.logStream)) {
                            server.container.logStream.unwatch();
                        }
                    }

                    Servers[this.json.uuid].setStatus(Status.OFF);
                }

                delete Servers[this.json.uuid];
                return callback();
            },
            // Delete the container (kills if running)
            delete_container: ['clear_object', (r, callback) => {
                this.log.debug('Attempting to remove container...');
                const service = new Nssm();
                service.remove(this.json.uuid).then( () => callback()).catch(e => callback())
            }],
            // Delete the configuration files for this server
            delete_config: ['clear_object', (r, callback) => {
                this.log.debug('Attempting to remove configuration files...');

                let pathToRemove = Path.join('./config/servers', this.json.uuid);
                if (Fs.existsSync(Path.join('./config/servers', this.json.uuid, 'install.log'))) {
                    this.log.debug('Not removing entire configuration folder because an installation log exists.');
                    pathToRemove = Path.join('./config/servers', this.json.uuid, 'server.json');
                }

                Fs.remove(pathToRemove, err => {
                    if (!err) this.log.debug('Removed configuration folder.');
                    return callback();
                });
            }],
            delete_folder: ['clear_object', (r, callback) => {
                Fs.remove(Path.join(Config.get('sftp.path', '/srv/daemon-data'), this.json.uuid), callback);
            }],
        }, err => {
            if (err) Log.fatal(err);
            if (!err) this.log.info('Server deleted.');

            return next(err);
        });
    }
}

module.exports = Delete;
