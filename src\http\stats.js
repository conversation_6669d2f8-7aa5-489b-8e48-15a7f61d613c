'use strict';

const rfr = require('rfr');
const Async = require('async');
const _ = require('lodash');

const ConfigHelper = rfr('src/helpers/config.js');
const Servers = rfr('src/helpers/initialize.js').Servers;
const Status = rfr('src/helpers/status.js');
const Socket = rfr('src/http/socket.js').Socket;

const Config = new ConfigHelper();

class Stats {
    constructor() {
        this.statSocket = Socket.of('/v1/stats/');
        this.statSocket.use((params, next) => {
            if (!params.handshake.query.token) {
                return next(new Error('You must pass the correct handshake values.'));
            }
            if (!_.isObject(Config.get('keys')) || !_.includes(Config.get('keys'), params.handshake.query.token)) {
                return next(new Error('Invalid handshake value passed.'));
            }
            return next();
        });
    }

    init() {
        setInterval(() => {
            this.send();
        }, 2000);
    }

    send() {
        const responseData = {};
        const statData = {
            memory: 0,
            cpu: 0,
            players: 0,
        };
        Async.each(Servers, (server, callback) => {
            responseData[server.json.uuid] = {
                container: server.json.container,
                service: server.json.service,
                status: server.status,
                query: server.processData.query,
                proc: server.processData.process,
            };
            if (server.status !== Status.OFF) {
                statData.memory += _.get(server.processData, 'process.memory.total', 0);
                statData.cpu += _.get(server.processData, 'process.cpu.total', 0);
                statData.players += _.get(server.processData, 'query.players.length', 0);
            }
            return callback();
        }, () => {
            this.statSocket.emit('live-stats', {
                'servers': responseData,
                'stats': statData,
            });
        });
    }
}

module.exports = Stats;
