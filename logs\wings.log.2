{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:00:28.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:00:28.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:00:28.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"|  Copyright 2015 - 2019 <PERSON>  |","time":"2022-08-17T13:00:28.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:00:28.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:00:28.021Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:00:28.982Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:00:29.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:00:29.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:00:29.615Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:00:30.248Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:00:30.310Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21468,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:81:33\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:163:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:158:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:154:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:00:58.211Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:01:59.823Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:01:59.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:01:59.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:01:59.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:01:59.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:01:59.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:02:00.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:02:00.716Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:02:00.837Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:02:00.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:02:01.458Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:02:01.511Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3864,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:81:33\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:170:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:165:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:02:02.956Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:02:31.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:02:31.819Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:02:31.819Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:02:31.819Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:02:31.819Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:02:31.819Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:02:32.831Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:02:32.852Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:02:32.980Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:02:32.986Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:02:33.618Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:02:33.672Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:81:33\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:169:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:165:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:02:34.575Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18840,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:81:33\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:169:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:165:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:02:36.778Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:05:32.940Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:05:32.941Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:05:32.941Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:05:32.941Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:05:32.941Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:05:32.941Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:05:33.841Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:05:33.861Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:05:33.978Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:05:33.984Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:05:34.603Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:05:34.657Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20784,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:171:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:165:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:05:38.346Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:06:02.635Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:06:02.635Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:06:02.636Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:06:02.636Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:06:02.636Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:06:02.636Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:06:03.619Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:06:03.639Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:06:03.775Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:06:03.780Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:06:04.388Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:06:04.440Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16144,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:171:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:165:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:06:07.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:06:48.321Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:06:48.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:06:48.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:06:48.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:06:48.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:06:48.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:06:49.299Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:06:49.320Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:06:49.443Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:06:49.448Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:06:50.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:06:50.130Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21336,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:173:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:167:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:161:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T13:06:50.317Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:07:24.370Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:07:24.371Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:07:24.371Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:07:24.371Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:07:24.371Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:07:24.371Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:07:25.398Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:07:25.419Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:07:25.530Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:07:25.535Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:07:26.213Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:07:26.269Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"callback is not a function","name":"TypeError","stack":"TypeError: callback is not a function\n    at Async.auto.update_startup (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:151:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at prepare (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:145:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1634:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at Object.auto (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1568:5)\n    at Server.modifyConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:107:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:80:32\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:56:28)\n    at RouteController.updateServerConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:73:19)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:106:12\n    at nextTick (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\chain.js:167:13)\n    at processTicksAndRejections (node:internal/process/task_queues:78:11)"},"msg":"callback is not a function","time":"2022-08-17T13:07:26.902Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18132,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"callback is not a function","name":"TypeError","stack":"TypeError: callback is not a function\n    at Async.auto.update_startup (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:151:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at prepare (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:145:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1634:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at Object.auto (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1568:5)\n    at Server.modifyConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:107:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:80:32\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:56:28)\n    at RouteController.updateServerConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:73:19)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:106:12\n    at nextTick (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\chain.js:167:13)\n    at processTicksAndRejections (node:internal/process/task_queues:78:11)"},"msg":"callback is not a function","time":"2022-08-17T13:07:31.543Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:08:59.877Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T13:08:59.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T13:08:59.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T13:08:59.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T13:08:59.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T13:08:59.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T13:09:00.876Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T13:09:00.896Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T13:09:01.025Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T13:09:01.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T13:09:01.646Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T13:09:01.703Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18448,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"callback is not a function","name":"TypeError","stack":"TypeError: callback is not a function\n    at Async.auto.update_startup (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:151:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at prepare (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:145:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1634:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at Object.auto (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1568:5)\n    at Server.modifyConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:107:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:80:32\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:56:28)\n    at RouteController.updateServerConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:73:19)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:106:12\n    at nextTick (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\chain.js:167:13)\n    at processTicksAndRejections (node:internal/process/task_queues:78:11)"},"msg":"callback is not a function","time":"2022-08-17T13:09:03.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:04:20.844Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:04:20.845Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:04:20.845Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:04:20.845Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:04:20.845Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:04:20.845Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:04:21.776Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:04:21.797Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:04:21.910Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:04:21.917Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:04:22.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:04:22.612Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8160,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:173:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:167:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:161:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:04:28.551Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:05:23.531Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:05:23.531Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:05:23.531Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:05:23.532Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:05:23.532Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:05:23.532Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:05:24.601Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:05:24.630Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:05:24.774Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:05:24.780Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:05:25.489Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:05:25.547Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21364,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:173:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:167:24)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:161:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:05:25.973Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:06:10.944Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:06:10.944Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:06:10.944Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:06:10.945Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:06:10.945Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:06:10.945Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:06:12.025Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:06:12.045Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:06:12.181Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:06:12.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:06:12.806Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:06:12.861Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21868,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:166:17)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:160:22","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:06:26.976Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:07:18.777Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:07:18.777Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:07:18.777Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:07:18.778Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:07:18.778Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:07:18.778Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:07:19.853Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:07:19.873Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:07:20.007Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:07:20.013Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:07:20.643Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21964,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:07:20.700Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:09:45.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:09:45.089Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:09:45.089Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:09:45.089Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:09:45.089Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:09:45.089Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:09:46.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:09:46.179Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:09:46.305Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:09:46.311Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:09:46.967Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:09:47.021Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3936,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:161:22"},"msg":"Callback was already called.","time":"2022-08-17T14:09:58.468Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:11:57.503Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:11:57.504Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:11:57.504Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:11:57.504Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:11:57.504Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:11:57.504Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:11:58.587Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:11:58.611Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:11:58.794Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:11:58.801Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:11:59.431Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:11:59.487Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22384,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:83:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:169:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.update_live (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:163:17)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:157:28","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:12:00.526Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:15:09.023Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:15:09.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:15:09.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:15:09.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:15:09.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:15:09.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:15:10.128Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:15:10.149Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:15:10.242Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:15:10.248Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:15:10.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18328,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:15:10.922Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:16:27.302Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:16:27.302Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:16:27.303Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:16:27.303Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:16:27.303Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:16:27.303Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21068,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:16:28.148Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:16:33.497Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:16:33.498Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:16:33.498Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:16:33.499Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:16:33.499Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:16:33.500Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:16:34.345Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:16:34.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:16:34.470Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:16:34.476Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:16:35.078Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:16:35.130Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:161:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:16:37.359Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18796,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:161:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:16:40.488Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:17:20.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:17:20.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:17:20.435Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:17:20.435Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:17:20.436Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:17:20.436Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:17:21.278Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:17:21.298Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:17:21.361Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:17:21.366Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:17:21.993Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21132,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:17:22.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:18:28.043Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:18:28.044Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:18:28.045Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:18:28.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:18:28.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:18:28.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:18:28.882Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:18:28.902Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:18:29.023Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:18:29.029Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:18:29.649Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:18:29.702Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:161:47\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:18:31.466Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3808,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:161:47\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:18:32.736Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:20:02.571Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:20:02.572Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:20:02.573Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:20:02.573Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:20:02.573Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:20:02.574Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:20:03.427Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:20:03.447Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:20:03.552Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:20:03.558Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:20:04.167Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:20:04.223Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:144:83\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:20:04.562Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:144:83\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:20:05.683Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:144:83\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:20:06.978Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:144:83\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:22:16.968Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22504,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:144:83\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:22:18.538Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:23:24.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:23:24.758Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:23:24.759Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:23:24.759Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:23:24.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:23:24.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:23:25.602Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:23:25.622Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:23:25.748Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:23:25.754Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:23:26.364Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:23:26.416Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:147:33\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:23:28.348Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21376,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:147:33\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:23:31.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:25:54.010Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:25:54.011Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:25:54.012Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:25:54.012Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:25:54.012Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:25:54.013Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:25:54.856Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:25:54.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:25:54.990Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:25:54.996Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:25:55.611Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:25:55.666Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:25:56.882Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:25:57.960Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:25:58.726Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:25:59.433Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:25:59.816Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.010Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.257Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.420Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.622Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.865Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:26:00.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:29:31.980Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:34:34.065Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:34:34.066Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:34:34.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:34:34.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:34:34.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:34:34.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:34:34.934Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:34:34.954Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:34:35.080Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:34:35.086Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:34:35.763Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:34:35.820Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot read properties of null (reading 'json')","name":"TypeError","stack":"TypeError: Cannot read properties of null (reading 'json')\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:113:97\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at Object.waterfall (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5334:5)\n    at Server.modifyConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:107:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:76:32\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:56:28)\n    at RouteController.updateServerConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:73:19)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:106:12\n    at nextTick (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\chain.js:167:13)\n    at processTicksAndRejections (node:internal/process/task_queues:78:11)"},"msg":"Cannot read properties of null (reading 'json')","time":"2022-08-17T14:34:38.298Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19036,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot read properties of null (reading 'json')","name":"TypeError","stack":"TypeError: Cannot read properties of null (reading 'json')\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:113:97\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at Object.waterfall (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5334:5)\n    at Server.modifyConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:107:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:76:32\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:56:28)\n    at RouteController.updateServerConfig (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:73:19)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:106:12\n    at nextTick (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\chain.js:167:13)\n    at processTicksAndRejections (node:internal/process/task_queues:78:11)"},"msg":"Cannot read properties of null (reading 'json')","time":"2022-08-17T14:34:39.659Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:04.034Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:38:04.034Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:38:04.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:38:04.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:04.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:38:04.037Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:38:04.909Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:38:04.929Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:38:05.062Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:38:05.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:38:05.697Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:38:05.752Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16016,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"next is not a function","name":"TypeError","stack":"TypeError: next is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"next is not a function","time":"2022-08-17T14:38:11.804Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:24.867Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:38:24.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:38:24.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:38:24.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:24.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:38:24.868Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22336,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:38:25.743Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:29.675Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:38:29.676Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:38:29.677Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:38:29.677Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:38:29.678Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:38:29.678Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:38:30.517Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:38:30.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:38:30.667Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:38:30.673Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:38:31.270Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:38:31.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:38:31.914Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20308,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:43\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:38:32.086Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:26.431Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:45:26.432Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:45:26.433Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:45:26.433Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:26.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:45:26.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:45:27.299Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:45:27.318Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:45:27.451Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:45:27.457Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:45:28.099Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4912,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:45:28.154Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:44.260Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:45:44.261Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:45:44.262Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:45:44.262Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:44.262Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:45:44.263Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:45:45.104Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:45:45.124Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:45:45.243Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:45:45.249Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:45:45.857Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23108,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:45:45.911Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:52.116Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:45:52.117Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:45:52.117Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:45:52.118Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:45:52.118Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:45:52.118Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:45:52.961Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:45:52.983Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:45:53.103Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:45:53.108Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:45:53.709Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:45:53.763Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22788,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"next is not a function","name":"TypeError","stack":"TypeError: next is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:24\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"next is not a function","time":"2022-08-17T14:45:54.811Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:46:14.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:46:14.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:46:14.904Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:46:14.905Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:46:14.905Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:46:14.906Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:46:15.755Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:46:15.775Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:46:16.013Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:46:16.019Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:46:16.625Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:46:16.678Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22708,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:77:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:152:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1578:20)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at Async.auto.write_config (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:148:17)\n    at runTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1632:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1572:13\n    at processQueue (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1582:13)\n    at taskComplete (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1601:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1625:17\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:24","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:46:17.227Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:49:15.423Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:49:15.424Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:49:15.425Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:49:15.425Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:49:15.426Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:49:15.426Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:49:16.282Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:49:16.302Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:49:16.441Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:49:16.447Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:49:17.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:49:17.131Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23092,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:77:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3888:9\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1006:25)\n    at iterateeCallback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:995:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:24\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:49:18.769Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:50:03.967Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:50:03.968Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:50:03.968Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:50:03.969Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:50:03.969Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:50:03.970Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:50:04.811Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:50:04.834Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:50:04.973Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:50:04.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:50:05.582Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:50:05.638Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22532,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:77:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3888:9\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1006:25)\n    at iterateeCallback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:995:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:24\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:50:07.452Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:50:43.647Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:50:43.647Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:50:43.648Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:50:43.648Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:50:43.649Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:50:43.649Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:50:44.494Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:50:44.514Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:50:44.647Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:50:44.652Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:50:45.254Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:50:45.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7036,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3888:9\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1006:25)\n    at iterateeCallback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:995:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:24\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:50:47.040Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:52:44.581Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:52:44.582Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:52:44.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:52:44.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:52:44.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:52:44.584Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:52:45.432Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:52:45.452Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:52:45.580Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:52:45.585Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:52:46.190Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:52:46.243Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:52:46.667Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:52:48.345Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:52:49.105Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:52:49.911Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7620,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:52:50.972Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:53:37.059Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:53:37.059Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:53:37.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:53:37.061Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:53:37.061Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:53:37.062Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:53:37.938Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:53:37.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:53:38.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:53:38.094Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:53:38.702Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:53:38.754Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":17896,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:110:76\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:53:39.931Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:54:57.801Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:54:57.801Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:54:57.802Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:54:57.803Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:54:57.803Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:54:57.804Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:54:58.650Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:54:58.670Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:54:58.796Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:54:58.802Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:54:59.402Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22636,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:54:59.454Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:57:03.105Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:57:03.106Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:57:03.106Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:57:03.106Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:57:03.107Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:57:03.107Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:57:03.954Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:57:03.974Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:57:04.093Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:57:04.098Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:57:04.737Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:57:04.793Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:46\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:57:04.922Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18460,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:46\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:57:10.074Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:57:25.957Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:57:25.957Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:57:25.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:57:25.959Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:57:25.959Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:57:25.959Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:57:26.812Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:57:26.832Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:57:26.952Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:57:26.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:57:27.559Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:57:27.612Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18456,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:46\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:57:28.562Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:58:37.915Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:58:37.915Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:58:37.916Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:58:37.917Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:58:37.917Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:58:37.917Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:58:38.756Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:58:38.776Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:58:38.899Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:58:38.905Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:58:39.521Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:58:39.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6712,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\service.js:162:46\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"Callback was already called.","time":"2022-08-17T14:58:40.940Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:59:38.825Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T14:59:38.826Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T14:59:38.827Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T14:59:38.827Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T14:59:38.828Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T14:59:38.828Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T14:59:39.685Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T14:59:39.704Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T14:59:39.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T14:59:39.827Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T14:59:40.440Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T14:59:40.494Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:158:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5329:29)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:152:24\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5331:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:112:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T14:59:40.556Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19092,"server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","level":40,"err":{"message":"C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json: Unexpected end of JSON input","name":"SyntaxError","stack":"SyntaxError: C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object._readFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\jsonfile\\index.js:25:16)"},"msg":"An error was detected with the changed file, attempting to undo the changes.","time":"2022-08-17T14:59:40.557Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:03:53.978Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:03:53.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:03:53.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:03:53.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:03:53.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:03:53.980Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:03:54.811Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:03:54.831Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:03:54.948Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:03:54.954Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:03:55.569Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:03:55.623Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:162:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5329:29)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:156:24\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5331:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:114:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T15:03:58.372Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:252:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:677:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:04:25.570Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23336,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:252:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:677:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:04:55.569Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:05:02.466Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:05:02.467Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:05:02.467Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:05:02.468Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:05:02.468Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:05:02.468Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:05:03.321Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:05:03.341Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:05:03.453Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:05:03.459Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:05:04.066Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:05:04.119Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:164:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5329:29)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:155:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T15:05:07.256Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:05:34.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:06:04.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:06:34.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:07:04.066Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:07:34.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:08:04.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:08:34.066Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:09:04.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:09:34.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:10:04.068Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:10:34.069Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:11:04.069Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:11:34.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:12:04.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:12:34.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:13:04.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:13:34.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:14:04.072Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot create property 'rebuild' on number '123'","name":"TypeError","stack":"TypeError: Cannot create property 'rebuild' on number '123'\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:137:35\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5331:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:114:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54"},"msg":"Cannot create property 'rebuild' on number '123'","time":"2022-08-17T15:14:32.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:254:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:679:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:14:34.072Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1348,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot create property 'rebuild' on number '123'","name":"TypeError","stack":"TypeError: Cannot create property 'rebuild' on number '123'\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:137:35\n    at nextTask (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5324:14)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5331:9)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:114:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54"},"msg":"Cannot create property 'rebuild' on number '123'","time":"2022-08-17T15:14:43.985Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:14:49.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:14:49.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:14:49.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:14:49.047Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:14:49.047Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:14:49.048Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:14:49.897Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:14:49.918Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:14:50.055Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:14:50.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":40,"msg":"Detected valid JSON, but server was missing a UUID in C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json, skipping...","time":"2022-08-17T15:14:50.063Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:14:50.063Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22552,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:14:50.116Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:15:22.729Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:15:22.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:15:22.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:15:22.732Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:15:22.732Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:15:22.733Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:15:23.573Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:15:23.594Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:15:23.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:15:23.741Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":40,"msg":"Detected valid JSON, but server was missing a UUID in C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json, skipping...","time":"2022-08-17T15:15:23.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:15:23.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19756,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:15:23.798Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:15:36.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:15:36.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:15:36.959Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:15:36.960Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:15:36.960Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:15:36.960Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:15:37.800Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:15:37.820Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:15:37.923Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:15:37.928Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":40,"msg":"Detected valid JSON, but server was missing a UUID in C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json, skipping...","time":"2022-08-17T15:15:37.930Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:15:37.931Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21532,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:15:37.983Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:17:45.005Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:17:45.005Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:17:45.006Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:17:45.007Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:17:45.007Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:17:45.008Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:17:45.893Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:17:45.915Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:17:46.058Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:17:46.064Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:17:46.689Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:17:46.743Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8352,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:158:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5329:29)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:150:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T15:17:58.402Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:18:26.280Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:18:26.281Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:18:26.282Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:18:26.283Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:18:26.283Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:18:26.283Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:18:27.124Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:18:27.144Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:18:27.253Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:18:27.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:18:27.880Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:18:27.932Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24264,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot remove headers after they are sent to the client","name":"Error","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot remove headers after they are sent to the client\n    at new NodeError (node:internal/errors:372:5)\n    at ServerResponse.removeHeader (node:_http_outgoing:654:11)\n    at ServerResponse.restifyWriteHead [as writeHead] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:647:18)\n    at flush (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:870:9)\n    at ServerResponse.__send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:424:20)\n    at ServerResponse.send (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\response.js:316:21)\n    at Responses.generic204 (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\helpers\\responses.js:35:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:79:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:159:20\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:473:16\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:5329:29)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:151:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\universalify\\index.js:22:54","code":"ERR_HTTP_HEADERS_SENT"},"msg":"Cannot remove headers after they are sent to the client","time":"2022-08-17T15:18:28.370Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:20:09.738Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:20:09.739Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:20:09.739Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:20:09.739Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:20:09.739Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:20:09.739Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:20:10.839Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:20:10.862Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:20:10.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:20:10.983Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:20:11.631Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:20:11.695Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21008,"level":60,"path":"/v1/server","method":"PATCH","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Command failed with exit code 3: C:\\Users\\<USER>\\AppData\\Roaming\\nssm\\nssm.exe set 9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2 AppParameters -profiles=profile -config=serverDZ.cfg -port=3322 -cpuCount=4 -dologs -adminlog -netlog -freezecheck\nC\u0000a\u0000n\u0000'\u0000t\u0000 \u0000o\u0000p\u0000e\u0000n\u0000 \u0000s\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000!\u0000\r\u0000\r\u0000\n\u0000O\u0000p\u0000e\u0000n\u0000S\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000(\u0000)\u0000:\u0000 \u0000�b�~���\u00020\r\u0000\r\u0000\n\u0000\r\u0000\r\u0000\n\u0000","name":"Error","stack":"Error: Command failed with exit code 3: C:\\Users\\<USER>\\AppData\\Roaming\\nssm\\nssm.exe set 9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2 AppParameters -profiles=profile -config=serverDZ.cfg -port=3322 -cpuCount=4 -dologs -adminlog -netlog -freezecheck\nC\u0000a\u0000n\u0000'\u0000t\u0000 \u0000o\u0000p\u0000e\u0000n\u0000 \u0000s\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000!\u0000\r\u0000\r\u0000\n\u0000O\u0000p\u0000e\u0000n\u0000S\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000(\u0000)\u0000:\u0000 \u0000�b�~���\u00020\r\u0000\r\u0000\n\u0000\r\u0000\r\u0000\n\u0000\n    at makeError (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\windows-service-wrapper\\node_modules\\execa\\lib\\error.js:59:11)\n    at handlePromise (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\windows-service-wrapper\\node_modules\\execa\\index.js:114:26)\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async Service.set (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\windows-service-wrapper\\src\\service.js:85:13)"},"msg":"Command failed with exit code 3: C:\\Users\\<USER>\\AppData\\Roaming\\nssm\\nssm.exe set 9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2 AppParameters -profiles=profile -config=serverDZ.cfg -port=3322 -cpuCount=4 -dologs -adminlog -netlog -freezecheck\nC\u0000a\u0000n\u0000'\u0000t\u0000 \u0000o\u0000p\u0000e\u0000n\u0000 \u0000s\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000!\u0000\r\u0000\r\u0000\n\u0000O\u0000p\u0000e\u0000n\u0000S\u0000e\u0000r\u0000v\u0000i\u0000c\u0000e\u0000(\u0000)\u0000:\u0000 \u0000�b�~���\u00020\r\u0000\r\u0000\n\u0000\r\u0000\r\u0000\n\u0000","time":"2022-08-17T15:20:34.122Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:20:58.609Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:20:58.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:20:58.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:20:58.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:20:58.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:20:58.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:20:59.716Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:20:59.740Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:20:59.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:20:59.882Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:21:00.556Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23676,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:21:00.624Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:24:01.385Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:24:01.386Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:24:01.386Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:24:01.386Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:24:01.386Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:24:01.386Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:24:02.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:24:02.194Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:24:02.327Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:24:02.333Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":40,"msg":"Detected valid JSON, but server was missing a UUID in C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\config\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\server.json, skipping...","time":"2022-08-17T15:24:02.336Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:24:02.337Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2652,"level":60,"err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:24:02.390Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:24:39.362Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:24:39.363Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:24:39.363Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:24:39.363Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:24:39.363Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:24:39.363Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:24:40.154Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:24:40.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:24:40.397Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:24:40.403Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:24:41.026Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16740,"level":60,"err":{"message":"Callback was already called.","name":"Error","stack":"Error: Callback was already called.\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:966:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:71:32\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:93:20\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:24:41.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:25:34.444Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:25:34.445Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:25:34.445Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:25:34.445Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:25:34.445Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:25:34.445Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:25:35.226Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:25:35.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:25:35.354Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:25:35.360Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:25:35.975Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22948,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:25:36.027Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:06.078Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:26:06.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:26:06.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:26:06.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:06.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:26:06.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:26:06.864Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:26:06.885Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:26:07.017Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:26:07.024Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:26:07.633Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:26:07.686Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23712,"level":60,"err":{"message":"The \"path\" argument must be of type string. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at new NodeError (node:internal/errors:372:5)\n    at validateString (node:internal/validators:120:11)\n    at Object.join (node:path:429:7)\n    at Server.path (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:255:31)\n    at FileSystem.size (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:69:29)\n    at Timeout.diskUse [as _onTimeout] (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:680:17)\n    at listOnTimeout (node:internal/timers:561:11)\n    at processTimers (node:internal/timers:502:7)","code":"ERR_INVALID_ARG_TYPE"},"msg":"A fatal error occured during an operation.","time":"2022-08-17T15:26:37.635Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:51.536Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:26:51.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:26:51.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:26:51.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:51.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:26:51.537Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:26:52.432Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:26:52.453Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:26:52.574Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23864,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:26:52.580Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:55.490Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:26:55.490Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:26:55.491Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:26:55.492Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:26:55.492Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:26:55.493Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:26:56.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:26:56.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:26:56.405Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:26:56.410Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:26:57.028Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23772,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:26:57.078Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:29:54.595Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:29:54.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:29:54.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:29:54.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:29:54.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:29:54.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21356,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:29:55.375Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:29:59.759Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:29:59.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:29:59.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:29:59.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:29:59.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:29:59.760Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:30:00.543Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:30:00.564Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:30:00.692Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:30:00.697Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:30:01.315Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7728,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:30:01.366Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:30:09.085Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:30:09.086Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:30:09.087Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:30:09.087Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:30:09.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:30:09.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:30:09.858Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:30:09.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:30:10.015Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:30:10.022Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:30:10.629Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23244,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:30:10.679Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:32:11.462Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:32:11.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:32:11.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:32:11.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:32:11.464Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:32:11.464Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24052,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:32:12.272Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:32:14.664Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:32:14.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:32:14.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:32:14.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:32:14.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:32:14.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:32:15.453Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:32:15.474Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:32:15.806Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:32:15.813Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:32:16.456Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22548,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:32:16.509Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:34:29.976Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:34:29.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:34:29.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:34:29.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:34:29.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:34:29.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:34:30.803Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:34:30.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:34:30.955Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:34:30.961Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:34:31.591Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23396,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:34:31.646Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:35:09.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:35:09.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:35:09.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:35:09.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:35:09.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:35:09.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:35:10.069Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:35:10.101Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:35:10.241Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:35:10.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:35:10.866Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23364,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:35:10.925Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:35:46.100Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:35:46.101Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:35:46.101Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:35:46.101Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:35:46.101Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:35:46.102Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:35:47.001Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:35:47.022Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:35:47.151Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:35:47.157Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:35:47.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24864,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:35:48.010Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:36:02.636Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:36:02.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:36:02.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:36:02.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:36:02.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:36:02.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:36:03.447Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:36:03.467Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:36:03.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:36:03.610Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:36:04.228Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25532,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:36:04.281Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:37:16.416Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:37:16.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:37:16.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:37:16.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:37:16.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:37:16.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:37:17.351Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:37:17.372Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:37:17.503Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:37:17.509Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:37:18.199Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22156,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:37:18.254Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:12.874Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:38:12.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:38:12.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:38:12.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:12.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:38:12.875Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:38:13.692Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:38:13.713Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:38:13.851Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:38:13.856Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:38:14.494Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:38:14.546Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:44.405Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:38:44.406Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:38:44.406Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:38:44.406Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:44.406Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:38:44.406Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:38:45.289Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:38:45.310Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:38:45.405Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:38:45.410Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:38:46.224Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12356,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:38:46.281Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:57.194Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:38:57.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:38:57.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:38:57.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:38:57.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:38:57.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:38:58.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:38:58.043Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:38:58.177Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:38:58.182Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:38:58.934Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:38:58.987Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":60,"path":{},"method":"GET","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot read properties of null (reading 'mime')","name":"TypeError","stack":"TypeError: Cannot read properties of null (reading 'mime')\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:439:60\n    at callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\polyfills.js:306:20)\n    at FSReqCallback.oncomplete (node:fs:199:5)\n    at FSReqCallback.callbackTrampoline (node:internal/async_hooks:130:17)"},"msg":"Cannot read properties of null (reading 'mime')","time":"2022-08-17T15:39:00.167Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4184,"level":60,"path":{},"method":"GET","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot read properties of null (reading 'mime')","name":"TypeError","stack":"TypeError: Cannot read properties of null (reading 'mime')\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:439:60\n    at callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\polyfills.js:306:20)\n    at FSReqCallback.oncomplete (node:fs:199:5)\n    at FSReqCallback.callbackTrampoline (node:internal/async_hooks:130:17)"},"msg":"Cannot read properties of null (reading 'mime')","time":"2022-08-17T15:39:00.170Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:39:43.806Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:39:43.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:39:43.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:39:43.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:39:43.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:39:43.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:39:44.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:39:44.717Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:39:44.859Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:39:44.865Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:39:45.728Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25100,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:39:45.812Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:40:20.830Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:40:20.830Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:40:20.830Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:40:20.830Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:40:20.831Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:40:20.831Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:40:21.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:40:21.658Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:40:21.810Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:40:21.816Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:40:22.446Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25464,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:40:22.501Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:42:53.059Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:42:53.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:42:53.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:42:53.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:42:53.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:42:53.060Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:42:53.945Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:42:53.966Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:42:54.093Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:42:54.099Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:42:54.745Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21820,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:42:54.801Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:46:10.436Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-17T15:46:10.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-17T15:46:10.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-17T15:46:10.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-17T15:46:10.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-17T15:46:10.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-17T15:46:11.276Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-17T15:46:11.297Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-17T15:46:11.444Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-17T15:46:11.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-17T15:46:12.226Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24948,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-17T15:46:12.304Z","v":0}
