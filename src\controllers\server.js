const EventEmitter = require('events').EventEmitter;
const Ansi = require('ansi-escape-sequences');
const Status = require('./../helpers/status');
const Path = require('path');
const Cache = require('memory-cache');
const Log = require('./../helpers/logger');
const Request = require('request');

const pidusage = require('pidusage')
const si = require('systeminformation');
const psTree = require('ps-tree')
const getSize = require('get-folder-size');
const rmdir = require('rimraf');
const Nssm = require('windows-service-wrapper').Service;

const ServiceCore = require('./../services/index');
const Errors = require('./../errors/index');
const Async = require('async');
const Websocket = require('./../http/socket').ServerSockets;
const UploadSocket = require('./../http/upload');
const _ = require("lodash");
const ConfigHelper = require('./../helpers/config');
const Querystring = require('querystring');
const OptionController = require('./option');
const FileSystem = require('./fs');
const extendify = require('extendify');
const Fs = require('fs-extra');
const Service = require('./service')
const Config = new ConfigHelper();
const SteamCMD = require('../steamDownloader/steamcmd')


class Server extends EventEmitter {
    constructor(json, next) {
        super();
        this.status = Status.OFF;
        this.json = json;
        this.uuid = this.json.uuid;
        this.processData = {
            query: {},
            process: {},
        };

        this.intervals = {
            process: null,
            query: null,
        };

        this.container = null;
        this.shouldRestart = false;
        this.knownWrite = false;
        this.buildInProgress = false;
        this.configDataLocation = Path.join(__dirname, '../../config/servers/', this.uuid);
        this.configLocation = Path.join(this.configDataLocation, 'server.json');
        this.containerInitialized = false;

        this.blockBooting = false;
        this.currentDiskUsed = 0;
        this.log = Log.child({server: this.uuid});
        this.lastCrash = undefined;
        this.fs = new FileSystem(this);
        this.service = new ServiceCore(this);

        Async.series([
            callback => {
                this.service.init().then(callback).catch(callback);
            },

            callback => {
                this.initContainer(err => {
                    if (err) {
                        console.log(err)
                        return callback(err);
                    }
                    return callback();
                });
            },
            callback => {
                this.socketIO = new Websocket(this).init();
                this.uploadSocket = new UploadSocket(this).init();
                this.option = new OptionController(this);

                // Check disk usage on construct and then check it every 30 seconds.
                this.diskUse(this);
                this.intervals.diskUse = setInterval(this.diskUse, Config.get('internals.disk_use_seconds', 30) * 1000, this);

                return callback();
            },
        ], next);
    }


    initContainer(next) {
        this.container = new Service(this, (err, status) => {
            return next(err);
        });
    }


    /**
     * Send output from server container to websocket.
     */
    output(output) {
        this.service.onConsole(output);
    }

    modifyConfig(object, overwrite, next) {

        if (_.isFunction(overwrite)) {
            next = overwrite; // eslint-disable-line
            overwrite = false; // eslint-disable-line
        }

        Async.waterfall([
            callback => {
                this.container.update(callback)
            },
            callback => {
                const deepExtend = extendify({
                    inPlace: false,
                    arrays: 'replace',
                });
                const newObject = (overwrite) ? _.assignIn(this.json, object) : deepExtend(this.json, object);
                // Ports are a pain in the butt.
                if (!_.isUndefined(newObject.build)) {
                    _.forEach(newObject.build, (obj, ident) => {
                        if (_.endsWith(ident, '|overwrite')) {
                            const item = _.split(ident, '|')[0];
                            newObject.build[item] = obj;
                            delete newObject.build[ident];
                        }
                    });
                }

                newObject.rebuild = (_.isUndefined(object.rebuild) || object.rebuild);

                // Update 127.0.0.1 to point to the docker0 interface.
                if (newObject.build.default.ip === '127.0.0.1') {
                    newObject.build.default.ip = Config.get('docker.network.ispn', false) ? '' : Config.get('docker.interface');
                }

                _.forEach(newObject.build.ports, (ports, ip) => {
                    if (ip === '127.0.0.1') {
                        if (!Config.get('docker.network.ispn', false)) {
                            newObject.build.ports[Config.get('docker.interface')] = ports;
                        }
                        delete newObject.build.ports[ip];
                    }
                });
                this.json = newObject
                Fs.outputJson(this.configLocation, newObject, {spaces: 2}, err => {
                    callback();
                });
            }
        ], function (err, result) {
            if (err) return next(err)
            return next()
        })


    }


    hasPermission(perm, token, next) {
        const tokenData = Cache.get(`auth:token:${token}`);
        if (_.isNull(tokenData)) {
            this.validateToken(token, (err, data) => {
                if (err) return next(err);

                if (_.get(data, 'server') !== this.uuid) {
                    return next(null, false, 'uuidDoesNotMatch');
                }

                Cache.put(`auth:token:${token}`, data, data.expires_at);

                return this.validatePermission(data, perm, next);
            });
        } else {
            return this.validatePermission(tokenData, perm, next);
        }
    }

    validatePermission(data, permission, next) {
        if (!_.isUndefined(permission)) {
            if (_.includes(data.permissions, permission) || _.includes(data.permissions, 's:*')) {
                // Check Suspension Status
                return next(null, !this.isSuspended(), 'isSuspended');
            }
        }
        return next(null, false, 'isUndefined');
    }

    validateToken(token, next) {
        Request.get({
            url: `${Config.get('remote.base')}/api/remote/authenticate/${token}`,
            headers: {
                'Accept': 'application/vnd.pterodactyl.v1+json',
                'Authorization': `Bearer ${Config.get('keys.0')}`,
            },
        }, (err, response, body) => {
            if (err) {
                return next(err);
            }

            if (response.statusCode === 404) {
                return next(null, {
                    expires: 0,
                    server: null,
                    permissions: [],
                });
            }

            if (response.statusCode !== 200) {
                this.log.warn({
                    responseCode: response.statusCode,
                    requestURL: `${Config.get('remote.base')}/api/remote/authenticate/[token hidden]`,
                }, 'An error was returned by the Panel while attempting to authenticate a server access token.');

                return next(new Error('Panel returned a non-200 response code while attempting to authenticate a token.'));
            }

            const data = JSON.parse(body);
            return next(null, {
                expires: _.get(data, 'data.attributes.expires_in'),
                server: _.get(data, 'data.id'),
                permissions: _.get(data, 'data.attributes.permissions'),
            });
        });
    }

    isSuspended() {
        return (_.get(this.json, 'suspended', 0) === 1);
    }

    blockStartup(shouldBlock, next) {
        this.blockBooting = (shouldBlock !== false);
        if (this.blockBooting) {
            this.log.warn('Server booting is now BLOCKED.');
        } else {
            this.log.info('Server booting is now UNBLOCKED.');
        }

        return next();
    }

    path(location) {
        const dataPath = Path.join(Config.get('sftp.path', '/srv/daemon-data'), this.json.uuid);

        if (_.isUndefined(location) || _.replace(location, /\s+/g, '').length < 1) {
            return dataPath;
        }

        // Dangerous path, do not rely on this as the final output location until running it through
        // the fs.realpath function.
        let returnPath = null;
        let nonExistentPathRoot = null;
        const resolvedPath = Path.join(dataPath, Path.normalize(Querystring.unescape(location)));

        try {
            returnPath = Fs.realpathSync(resolvedPath);
        } catch (err) {
            // Errors that aren't ENOENT should generally just be reported to the daemon output, I'm not
            // really sure what they could possibly be, but we should log it anyways and then
            // just return the default data path for the server. When this happens, pray that the
            // calling function is smart enough to do a stat and determine if the operation
            // is happening aganist a directory, otherwise say hello to an ESDIR error code.
            if (err.code !== 'ENOENT') {
                this.log.error(err);
                return dataPath;
            }

            const minLength = _.split(dataPath, '/').length;
            const pathParts = _.split(Path.dirname(resolvedPath), '/');

            // Prevent malicious users from trying to slow things down with wild paths.
            if (pathParts.length > 50) {
                this.log.error('Attempting to determine path resolution on a directory nested more than 50 levels deep.');
                return dataPath;
            }

            // Okay, so the path being requested initially doesn't seem to exist on the system. Now
            // we will loop over the path starting at the end until we reach a directory that does exist
            // and check the final path resoltuion for that directory. If there is an unexpected error or
            // we get to a point where the path is now shorter than the data path we will exit and return
            // the base data path.
            _.some(pathParts, () => {
                if (pathParts.length < minLength) {
                    return true;
                }

                try {
                    nonExistentPathRoot = Fs.realpathSync(pathParts.join('/'));
                    return true;
                } catch (loopError) {
                    if (loopError.code !== 'ENOENT') {
                        this.log.error(loopError);
                        return true;
                    }

                    // Pop the last item off the checked path so we can check one level up on
                    // the next loop iteration.
                    pathParts.pop();
                }

                return false;
            });
        }

        // If we had to traverse the filesystem to resolve a path root we should check if the final
        // resolution is within the server data directory. If so, return at this point, otherwise continue
        // on and check aganist the normal return path.
        if (!_.isNull(nonExistentPathRoot)) {
            if (_.startsWith(nonExistentPathRoot, dataPath)) {
                return resolvedPath;
            }
        }

        if (_.startsWith(returnPath, dataPath)) {
            return returnPath;
        }

        // If we manage to get to this point something has gone seriously wrong and the user
        // is likely attempting to escape the system. Just throw a fatal error and pray that
        // the rest of the daemon is able to handle this.
        const e = new Error(`could not resolve a valid server data path for location ["${Querystring.unescape(location)}"]`);
        e.code = 'P_SYMLINK_RESOLUTION';

        throw e;
    }


    suspend(next) {
        Async.parallel([
            callback => {
                this.modifyConfig({suspended: 1}, callback);
            },
            callback => {
                if (this.status !== Status.OFF) {
                    return this.kill(callback);
                }
                return callback();
            },
        ], err => {
            if (!err) {
                this.log.warn('Server has been suspended.');
            }
            return next(err);
        });
    }


    unsuspend(next) {
        Async.parallel([
            callback => {
                this.modifyConfig({suspended: 0}, callback);
            },
        ], err => {
            if (!err) {
                this.log.info('Server has been unsuspended.');
            }
            return next(err);
        });
    }

    process(self) { // eslint-disable-line
        if (self.status === Status.OFF) return;

        const dataPath = Path.join(Config.get('sftp.path', '/srv/daemon-data'), self.json.uuid);

        Async.auto({
            get_service_info: (callback) => {
                si.services(self.json.uuid).then(res => callback(null, res[0])).catch(e => callback(e))
            },
            get_children_pid: ['get_service_info', (res, callback) => {
                const service_info = _.get(res, 'get_service_info')
                const pids = _.get(service_info, 'pids')
                if (pids[0] === '0') {
                    callback(new Error("pid was not found"))
                } else {
                    callback(null, pids[0])
                }
            }],
            pid_detail: ['get_children_pid', (res, callback) => {
                const ppid = _.get(res, 'get_children_pid')
                psTree(ppid, (err, children) => {
                    if (err) callback(new Error(err))
                    let pid = 0

                    try {
                        pid = children[0].PID
                    } catch (e) {
                        callback(new Error(e))
                    }

                    pidusage(pid, (err, stats) => {
                        if (err) return callback(new Error(err))
                        return callback(null, {
                            cpu: {
                                cores: {},
                                limit: self.json.build.cpu,
                                total: stats.cpu
                            },
                            memory: {
                                total: (stats.memory / 1024) / 1024,
                                cmax: self.json.build.memory * 1000000,
                                amax: self.json.build.memory * 1000000,
                            },
                            network: {
                                eth0: {
                                    tx_dropped: 0
                                }
                            }

                        })

                    })


                })
            }],
            disk_usage: ['pid_detail', (res, callback) => {
                callback(null, {
                    disk: {
                        used: self.currentDiskUsed,
                        limit: self.json.build.disk,
                        io_limit: self.json.build.io
                    }
                })
            }]
        }, (err, data) => {
            if (err) {
                self.emit('proc', self.processData.process);
            } else {
                self.processData.process = { // eslint-disable-line
                    memory: {
                        total: data.pid_detail.memory.total,
                        cmax: data.pid_detail.memory.cmax / 1000000,
                        amax: data.pid_detail.memory.amax / 1000000,
                    },
                    cpu: {
                        cores: {},
                        total: parseFloat(data.pid_detail.cpu.total.toFixed(3).toString()),
                        limit: data.pid_detail.cpu.limit,
                    },
                    disk: {
                        used: data.disk_usage.disk.used,
                        limit: data.disk_usage.disk.limit,
                        io_limit: data.disk_usage.disk.io_limit
                    },
                    network: data.pid_detail.network
                }
                self.emit('proc', self.processData.process);
            }


        })

    }


    setStatus(status) {
        if (status === this.status) return;
        const inverted = _.invert(Status);

        // If a user starts their server and then tries to stop it before the server is done
        // it triggers a crash condition. This logic determines if the server status is set to
        // stopping, and if so, we prevent changing the status to starting or on. This allows the
        // server to not crash when we press stop before it is compeltely started.
        if (this.status === Status.STOPPING && (status === Status.ON || status === Status.STARTING)) {
            this.log.debug(`Recieved request to mark server as ${inverted[status]} but the server is currently marked as STOPPING.`);
            return;
        }

        // Handle Internal Tracking
        if (status === Status.ON || status === Status.STARTING) {
            if (_.isNull(this.intervals.process)) {
                // Go ahead and run since it will be a minute until it does anyways.
                // Only run if the container is all initialized and ready to go though.
                if (this.containerInitialized) {
                    setTimeout(this.diskUse, 2000, this);
                }

                this.intervals.process = setInterval(this.process, 2000, this);
            }
        } else if (status === Status.STOPPING || status === Status.OFF) {
            if (!_.isNull(this.intervals.process)) {
                // Server is stopping or stopped, lets clear the interval as well as any stored
                // information about the process. Lets also detach the stats stream.
                clearInterval(this.intervals.process);
                this.intervals.process = null;
                this.processData.process = {};
            }
        }

        switch (status) {
            case Status.OFF:
                this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server marked as ${Ansi.style.bold}OFF`);
                break;
            case Status.ON:
                this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server marked as ${Ansi.style.bold}ON`);
                break;
            case Status.STARTING:
                this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server marked as ${Ansi.style.bold}STARTING`);
                break;
            case Status.STOPPING:
                this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server marked as ${Ansi.style.bold}STOPPING`);
                break;
            default:
                break;
        }

        this.log.info(`Server status has been changed to ${inverted[status]}`);
        this.status = status;
        this.emit(`is:${inverted[status]}`);
        this.emit('status', status);
    }

    checking_update(next) {
        const update_switch = _.get(this.json, 'build.env.AUTO_UPDATE')
        if (update_switch === 'true') {

            const options = {}
            options.username = _.get(this.json, 'build.env.STEAM_USERNAME', 'anonymous');
            if (_.get(this.json, 'build.env.STEAM_PASSWORD')) {
                options.password = _.get(this.json, 'build.env.STEAM_PASSWORD')
            }

            const dataPath = Path.join(Config.get('sftp.path', '/srv/daemon-data'), this.json.uuid);

            options.appId = _.get(this.json, 'build.env.STEAM_APP_ID')
            options.path = dataPath

            this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server enable auto update, try to updating server files. please waiting a moment.`);
            SteamCMD.install(options, next)

        } else {

            this.emit('console', `${Ansi.style.cyan}[V2PG Daemon] Server not enable auto update, skipping now.`);
            return next()

        }


    }

    preflight(next) {
        // Return immediately if server is currently powered on.
        if (this.status !== Status.STARTING) {
            return next(new Error('Server must be in starting state to run preflight.'));
        }

        this.service.onPreflight().then(next).catch(err => {
            if (err instanceof Errors.FileParseError) {
                this.emit('console', `${Ansi.style.yellow}[V2PG Daemon] Encountered an error while processing ${err.file} -- this could lead to issues running the server.`);
                this.emit('console', `${Ansi.style.yellow}[V2PG Daemon] ${err.message}`);

                return next();
            }

            if (err instanceof Errors.NoEggConfigurationError) {
                this.emit('console', `${Ansi.style['bg-red']}${Ansi.style.white}[V2PG Daemon] No server egg configuration could be located; aborting startup.`);
            }

            return next(err);
        });
    }


    start(next) {
        if (this.status !== Status.OFF) {
            return next(new Error('Server is already running.'));
        }

        if (this.blockBooting) {
            return next(new Error('Server cannot be started, booting is blocked due to pack or egg install.'));
        }

        this.setStatus(Status.STARTING);


        // Set status early on to avoid superfast clickers
        Async.series([
            callback => {
                this.log.debug('Checking size of server folder before booting.');
                this.emit('console', `${Ansi.style.yellow}[V2PG Daemon] Checking size of server data directory...`);
                this.fs.size((err, size) => {
                    if (err) return callback(err);

                    // 10 MB overhead accounting.
                    const sizeInMb = Math.round(size / (1000 * 1000));
                    this.currentDiskUsed = sizeInMb;

                    if (this.json.build.disk > 0 && sizeInMb > this.json.build.disk) {
                        this.emit('console', `${Ansi.style.yellow}[V2PG Daemon] Not enough disk space! ${sizeInMb}M / ${this.json.build.disk}M`);
                        return callback(new Error('There is not enough available disk space to start this server.'));
                    }

                    this.emit('console', `${Ansi.style.yellow}[V2PG Daemon] Disk Usage: ${sizeInMb}M / ${this.json.build.disk}M`);
                    return callback();
                });
            },

            callback => {
                this.log.debug('Checking for updates.');
                this.emit('console', `${Ansi.style.green}[V2PG Daemon] Checking for updates.`);
                this.checking_update(callback);
            },
            callback => {
                this.log.debug('Initializing for boot sequence, running preflight checks.');
                this.emit('console', `${Ansi.style.green}[V2PG Daemon] Running server preflight.`);
                this.preflight(callback);
            },
            callback => {
                this.emit('console', `${Ansi.style.green}[V2PG Daemon] Starting server container.`);
                this.container.start(callback);
            },
        ], err => {
            if (err) {
                this.setStatus(Status.OFF);
                this.emit('console', `${Ansi.style.red}[V2PG Daemon] A fatal error was encountered while starting this server.`);
                this.log.error(err);
                return next(err);
            }

            return next();
        });
    }

    /**
     * Stop a server using the defined egg stop capabilities.
     *
     * @param {Function} next
     * @return {void|Function}
     */
    stop(next) {
        if (this.status === Status.OFF) {
            return next();
        }

        if (_.isUndefined(_.get(this.service, 'config.stop'))) {
            this.emit('console', `${Ansi.style.red}[V2PG Daemon] No stop configuration is defined for this egg.`);
            return next();
        }

        this.setStatus(Status.STOPPING);

        // So, technically docker sends a SIGTERM to the process when this is run.
        // This works out fine normally, however, there are times when a container might take
        // more than 10 seconds to gracefully stop, at which point docker resorts to sending
        // a SIGKILL, which, as you can imagine, isn't ideal for stopping a server.
        //
        // So, what we will do is send a stop command, and then sit there and wait
        // until the container stops because the process stopped, at which point the crash
        // detection will not fire since we set the status to STOPPING.
        const stopCommand = _.get(this.service, 'config.stop');

        // Maintain backwards compatability with old eggs that used ^C as a way of telling a container
        // to stop using docker's default stop setup.
        if (stopCommand === '^C') {
            this.container.stop(next);
        }

        // // If the stop command begins with a "^" we will interpret that as the desire to
        // // send a specific signal to the container to stop the application.
        // if (_.startsWith(stopCommand, '^')) {
        //     this.docker.stopWithSignal(_.replace(stopCommand, '^', '')).then(next).catch(next);
        //
        //     return;
        // }
        //
        // // Send the command to the instance to begin the shutdown procedures.
        // this.docker.write(stopCommand).then(next).catch(next);
    }


    // Still using self here because of intervals.
    query() {
        return _.noop();
    }

    reinstall(config, next) {

        let filePath = Config.get('sftp.path', '/srv/daemon-data');

        this.log.info('starting reinstall ...');
        const nssm = new Nssm()

        Async.series([

            callback => {
                this.stop(callback);
            },
            callback => {
                if (this.status !== Status.OFF) {
                    this.once('is:OFF', callback);
                } else {
                    return callback();
                }
            },
            callback => {
                if (_.isObject(config) && !_.isEmpty(config)) {
                    this.modifyConfig(config, false, callback);
                } else {
                    return callback();
                }
            },

            callback => {
                // remove service
                this.log.info('checking service ...');

                nssm.check(this.json.uuid).then(res => callback()).catch(e => callback(e))
            },
            callback => {
                this.log.info('removing service ...');

                nssm.remove(this.json.uuid).then(() => callback()).catch(e => callback(e))
            },
            callback => {
                /**
                 * 清空当前数据文件夹
                 */
                let dicPath = Path.join(filePath, this.json.uuid);

                rmdir(dicPath, function (error) {
                    if (!error) {
                        Fs.ensureDir(dicPath, err => {
                            console.log(err) // => null
                        })
                    }
                });

                Fs.emptyDir(dicPath).then(() => {
                    console.log('clear dirs success!')
                }).catch(err => {
                    console.error(err)
                })

                callback()
            },

            callback => {
            }
        ], next);
    }


    kill(next) {
        if (this.status === Status.OFF) {
            return next(new Error('Server is already stopped.'));
        }

        this.setStatus(Status.STOPPING);
        this.container.stop(err => {
            this.setStatus(Status.OFF);
            this.emit('console', `${Ansi.style['bg-red']}${Ansi.style.white}[V2PG Daemon] Server marked as ${Ansi.style.bold}KILLED.`);
            return next(err);
        });
    }

    restart(next) {
        if (this.status !== Status.OFF) {
            this.shouldRestart = true;
            this.stop(next);
        } else {
            this.start(next);
        }
    }


    diskUse(self) { // eslint-disable-line
        self.fs.size((err, size) => {
            if (err) return self.log.warn(err);

            self.currentDiskUsed = Math.round(size / (1000 * 1000)); // eslint-disable-line
            if (self.json.build.disk > 0 && size > (self.json.build.disk * 1000 * 1000) && self.status !== Status.OFF) {
                self.emit('console', `${Ansi.style.red}[V2PG Daemon] Server is violating disk space limits. Stopping process.`);

                if (Config.get('actions.disk.kill', true)) {
                    self.kill(killErr => {
                        if (killErr) self.log.error(killErr);
                    });
                } else {
                    self.stop(stopErr => {
                        if (stopErr) self.log.error(stopErr);
                    });
                }
            }
        });
    }

}

module.exports = Server;
