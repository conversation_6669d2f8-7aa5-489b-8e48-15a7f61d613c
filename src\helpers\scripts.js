'use strict';

const _ = require('lodash')
const Fs = require('fs-extra')
const Path = require('path');
const rfr = require("rfr");
const ConfigHelper = rfr('src/helpers/config.js');
const Config = new ConfigHelper();

class Scripts {

    run(uuid, script) {
        const serverPath = Path.join(Config.get('sftp.path', ''), uuid);
        const commandType = _.get(script, 'type', null)
        if (!commandType) {
            return Error('command not defined')
        }

        try {
            let target = _.get(script, 'target', '')

            switch (commandType) {
                case 'mkdir':
                    Fs.ensureDirSync(Path.join(serverPath, target))
                    break;
                case 'cp':
                    const from = _.get(script, 'from', '')
                    const to = _.get(script, 'to', '')
                    Fs.copySync(Path.join(serverPath, from), Path.join(serverPath, to))
                    break;
                case 'rm':
                    Fs.removeSync(Path.join(serverPath, target))
                    break;
                case 'writefile':
                    const text = _.get(script, 'text', '')
                    const encoding = _.get(script, 'encoding', 'utf8')
                    Fs.writeFile(Path.join(serverPath, target), text, {encoding: encoding}, (err) => {
                        if (err) {
                            throw new Error(err)
                        }
                    })
                    break


            }
        } catch (e) {
            console.error(e)
        }


    }

}

module.exports = Scripts
