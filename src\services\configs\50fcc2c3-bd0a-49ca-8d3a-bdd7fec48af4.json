{"startup": {"done": "Listening on ", "userInteraction": ["Listening on /0.0.0.0:25577"]}, "stop": "end", "configs": {"config.yml": {"parser": "yaml", "find": {"listeners[0].query_enabled": true, "listeners[0].query_port": "{{server.build.default.port}}", "listeners[0].host": "0.0.0.0:{{server.build.default.port}}", "servers.*.address": {"127.0.0.1": "{{config.docker.interface}}", "localhost": "{{config.docker.interface}}"}}}}, "log": {"custom": false, "location": "proxy.log.0"}, "query": "none"}