{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:10:30.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T12:10:30.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T12:10:30.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"|  Copyright 2015 - 2019 <PERSON>  |","time":"2022-08-18T12:10:30.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:10:30.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T12:10:30.071Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T12:10:30.999Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T12:10:31.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T12:10:31.776Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T12:10:31.784Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T12:10:32.446Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19708,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T12:10:32.509Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:58:04.769Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T12:58:04.770Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T12:58:04.770Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T12:58:04.770Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:58:04.770Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T12:58:04.770Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T12:58:05.712Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T12:58:05.732Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T12:58:05.864Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T12:58:05.869Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T12:58:06.580Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T12:58:06.637Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:109:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aa.txt'","time":"2022-08-18T12:58:09.244Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:109:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'","time":"2022-08-18T12:58:32.454Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:109:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa'","time":"2022-08-18T12:58:33.572Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7764,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:109:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","time":"2022-08-18T12:58:42.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:59:57.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T12:59:57.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T12:59:57.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T12:59:57.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T12:59:57.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T12:59:57.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T12:59:58.694Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T12:59:58.715Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T12:59:58.837Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T12:59:58.843Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T12:59:59.458Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T12:59:59.511Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:105:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","time":"2022-08-18T13:00:06.717Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:105:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","time":"2022-08-18T13:00:07.722Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:105:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","time":"2022-08-18T13:00:08.613Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3700,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","name":"Error","stack":"Error: ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'\n    at Object.openSync (node:fs:585:3)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:105:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1016:9\n    at eachOfLimit (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1041:24)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1046:16\n    at _parallel (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3879:5)\n    at Object.series (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:4735:5)\n    at FileSystem.write (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:103:15)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:152:35\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:76:32\n    at Server.validatePermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:180:24)\n    at Server.hasPermission (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\server.js:172:25)\n    at AuthorizationMiddleware.allowed (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:68:36)\n    at RouteController.postServerFile (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\routes.js:149:19)\n    at Server.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:152:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\restify\\lib\\server.js:912:30)\n    at f (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\once\\once.js:25:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\http\\routes.js:58:16\n    at AuthorizationMiddleware.init (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\middleware\\authorizable.js:22:16)","code":"ENOENT"},"msg":"ENOENT: no such file or directory, open 'G:\\servers\\9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2\\aaa.txt'","time":"2022-08-18T13:00:17.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:03:28.407Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:03:28.408Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:03:28.408Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:03:28.408Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:03:28.408Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:03:28.409Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:03:29.351Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:03:29.372Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:03:29.478Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:03:29.484Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:03:30.121Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:03:30.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12952,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined\n    at Object.openSync (node:fs:577:10)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:110:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at iterateeCallback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:995:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:106:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\fs-extra\\lib\\ensure\\file.js:12:7\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\graceful-fs.js:143:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\graceful-fs.js:61:14\n    at FSReqCallback.oncomplete (node:fs:188:23)\n    at FSReqCallback.callbackTrampoline (node:internal/async_hooks:130:17)","code":"ERR_INVALID_ARG_TYPE"},"msg":"The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined","time":"2022-08-18T13:03:33.709Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:03:53.049Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:03:53.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:03:53.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:03:53.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:03:53.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:03:53.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:03:53.968Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:03:53.989Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:03:54.105Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:03:54.111Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:03:54.715Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:03:54.766Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":12812,"level":60,"path":"/v1/server/file/save","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined","name":"TypeError","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined\n    at Object.openSync (node:fs:577:10)\n    at Object.readFileSync (node:fs:453:35)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:112:39\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3880:24\n    at replenish (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:1011:17)\n    at iterateeCallback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:995:17)\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:969:16\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\async\\dist\\async.js:3885:13\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:108:21\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\fs-extra\\lib\\ensure\\file.js:17:40\n    at callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\polyfills.js:306:20)\n    at FSReqCallback.oncomplete (node:fs:199:5)\n    at FSReqCallback.callbackTrampoline (node:internal/async_hooks:130:17)","code":"ERR_INVALID_ARG_TYPE"},"msg":"The \"path\" argument must be of type string or an instance of Buffer or URL. Received undefined","time":"2022-08-18T13:03:55.078Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:04:28.501Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:04:28.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:04:28.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:04:28.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:04:28.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:04:28.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:04:29.415Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:04:29.435Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:04:29.538Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:04:29.543Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:04:30.164Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7664,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:04:30.217Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:04:55.069Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:04:55.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:04:55.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:04:55.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:04:55.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:04:55.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:04:55.945Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:04:55.965Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:04:56.106Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:04:56.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:04:56.720Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22024,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:04:56.779Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:07:20.072Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:07:20.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:07:20.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:07:20.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:07:20.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:07:20.073Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:07:20.966Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:07:20.987Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:07:21.191Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:07:21.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:07:21.814Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19808,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:07:21.867Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:11:37.336Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:11:37.336Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:11:37.337Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:11:37.337Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:11:37.337Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:11:37.337Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:11:38.271Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:11:38.294Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:11:38.428Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:11:38.436Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:11:39.084Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7880,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:11:39.138Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:11:55.001Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:11:55.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:11:55.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:11:55.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:11:55.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:11:55.002Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:11:55.933Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:11:55.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:11:56.097Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:11:56.103Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:11:56.715Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4944,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:11:56.768Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:14:14.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:14:14.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:14:14.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:14:14.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:14:14.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:14:14.308Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:14:15.184Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:14:15.205Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:14:15.273Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:14:15.279Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:14:15.899Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22180,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:14:15.951Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:14:21.598Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:14:21.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:14:21.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:14:21.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:14:21.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:14:21.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:14:22.439Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:14:22.460Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:14:22.622Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:14:22.627Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:14:23.342Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20088,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:14:23.394Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:17:13.206Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:17:13.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:17:13.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:17:13.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:17:13.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:17:13.207Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:17:14.046Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:17:14.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:17:14.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:17:14.192Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:17:14.801Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4556,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:17:14.853Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:17:53.897Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:17:53.898Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:17:53.898Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:17:53.898Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:17:53.898Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:17:53.899Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:17:55.118Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:17:55.144Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:17:55.281Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:17:55.288Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:17:55.948Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5768,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:17:56.058Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:22:02.603Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:22:02.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:22:02.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:22:02.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:22:02.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:22:02.605Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:22:03.516Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:22:03.542Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:22:03.669Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:22:03.676Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:22:04.310Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3632,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:22:04.405Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:22:41.718Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:22:41.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:22:41.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:22:41.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:22:41.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:22:41.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:22:42.680Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:22:42.705Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:22:42.811Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:22:42.817Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:22:43.460Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:22:43.551Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:23:02.792Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:23:02.792Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:23:02.793Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:23:02.793Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:23:02.793Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:23:02.793Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:23:03.802Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:23:03.831Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:23:04.026Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:23:04.033Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:23:04.663Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5420,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:23:04.754Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:23:55.194Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:23:55.194Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:23:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:23:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:23:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:23:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:23:56.115Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:23:56.137Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:23:56.254Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:23:56.260Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:23:56.925Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6052,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:23:56.977Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:06.841Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:25:06.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:25:06.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:25:06.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:06.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:25:06.842Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:25:07.791Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:25:07.813Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:25:07.928Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:25:07.934Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:25:08.602Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24608,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:25:08.670Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:25.040Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:25:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:25:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:25:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:25:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:25:25.973Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:25:25.994Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:25:26.123Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:25:26.129Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:25:26.732Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24320,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:25:26.784Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:55.257Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:25:55.257Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:25:55.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:25:55.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:25:55.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:25:55.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:25:56.144Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:25:56.164Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:25:56.283Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:25:56.288Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:25:56.908Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21844,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:25:56.963Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:26:18.734Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:26:18.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:26:18.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:26:18.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:26:18.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:26:18.735Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:26:19.605Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:26:19.625Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:26:19.751Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:26:19.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:26:20.361Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24936,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:26:20.412Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:28:23.836Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:28:23.836Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:28:23.836Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:28:23.837Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:28:23.837Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:28:23.837Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:28:24.782Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:28:24.803Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:28:25.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:28:25.041Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:28:25.750Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23260,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:28:25.805Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:28:55.194Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:28:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:28:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:28:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:28:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:28:55.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:28:56.067Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:28:56.087Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:28:56.197Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:28:56.203Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:28:56.810Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3932,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:28:56.863Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:30:55.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:30:55.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:30:55.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:30:55.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:30:55.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:30:55.259Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:30:56.105Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:30:56.125Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:30:56.254Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:30:56.260Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:30:56.889Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:30:56.942Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3324,"level":60,"path":{},"method":"GET","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"Cannot read properties of null (reading 'mime')","name":"TypeError","stack":"TypeError: Cannot read properties of null (reading 'mime')\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\fs.js:286:54\n    at callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\graceful-fs\\polyfills.js:306:20)\n    at FSReqCallback.oncomplete (node:fs:199:5)\n    at FSReqCallback.callbackTrampoline (node:internal/async_hooks:130:17)"},"msg":"Cannot read properties of null (reading 'mime')","time":"2022-08-18T13:31:20.467Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:32:32.957Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:32:32.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:32:32.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:32:32.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:32:32.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:32:32.958Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:32:33.862Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:32:33.883Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:32:33.991Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:32:33.997Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:32:34.694Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25360,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:32:34.750Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:33:27.821Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:33:27.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:33:27.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:33:27.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:33:27.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:33:27.822Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:33:28.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:33:28.787Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:33:28.918Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:33:28.924Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:33:29.556Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19168,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:33:29.611Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:34:08.695Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:34:08.695Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:34:08.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:34:08.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:34:08.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:34:08.696Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:34:09.607Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:34:09.628Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:34:09.753Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:34:09.758Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:34:10.391Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":14016,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:34:10.478Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:34:37.485Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:34:37.486Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:34:37.486Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:34:37.486Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:34:37.486Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:34:37.486Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:34:38.329Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:34:38.349Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:34:38.458Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:34:38.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:34:39.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25404,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:34:39.123Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:35:13.575Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:35:13.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:35:13.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:35:13.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:35:13.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:35:13.576Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:35:14.441Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:35:14.463Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:35:14.591Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:35:14.597Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:35:15.224Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22864,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:35:15.278Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:35:29.449Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:35:29.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:35:29.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:35:29.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:35:29.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:35:29.450Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:35:30.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:35:30.307Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:35:30.434Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:35:30.440Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:35:31.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19664,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:35:31.102Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:37:01.377Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:37:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:37:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:37:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:37:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:37:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:37:02.193Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:37:02.213Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:37:02.332Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:37:02.338Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:37:02.965Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":1624,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:37:03.025Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:38:19.707Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:38:20.497Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:38:20.518Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:38:20.624Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:38:20.630Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:38:21.277Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24924,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:38:21.366Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:40:59.952Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:40:59.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:40:59.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:40:59.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:40:59.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:40:59.953Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:41:00.887Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:41:00.909Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:41:01.050Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:41:01.055Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:41:01.688Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:41:01.743Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:42:24.157Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:42:24.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:42:24.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:42:24.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:42:24.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:42:24.158Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:42:25.082Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:42:25.105Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:42:25.256Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:42:25.262Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:42:25.980Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23976,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:42:26.037Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:44:42.806Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:44:42.806Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:44:42.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:44:42.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:44:42.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:44:42.807Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:44:43.727Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:44:43.748Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:44:43.970Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:44:43.975Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:44:44.616Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":16288,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:44:44.671Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:45:14.918Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:45:14.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:45:14.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:45:14.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:45:14.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:45:14.919Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:45:15.785Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:45:15.808Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:45:16.076Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:45:16.081Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:45:16.754Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":588,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:45:16.809Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:50:52.416Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:50:52.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:50:52.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:50:52.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:50:52.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:50:52.417Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:50:53.306Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:50:53.328Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:50:53.474Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:50:53.479Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:50:54.097Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21776,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:50:54.150Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:51:54.266Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:51:54.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:51:54.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:51:54.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:51:54.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:51:54.267Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:51:55.261Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:51:55.285Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:51:55.412Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:51:55.418Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:51:56.074Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5380,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:51:56.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:53:05.554Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T13:53:05.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T13:53:05.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T13:53:05.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T13:53:05.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T13:53:05.555Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T13:53:06.444Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T13:53:06.465Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T13:53:06.600Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T13:53:06.606Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T13:53:07.269Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24968,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T13:53:07.322Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:06:08.817Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:06:08.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:06:08.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:06:08.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:06:08.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:06:08.818Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:06:09.775Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:06:09.796Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:06:09.914Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:06:09.920Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:06:10.606Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22172,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:06:10.661Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:16:40.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:16:40.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:16:40.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:16:40.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:16:40.031Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:16:40.032Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:16:40.935Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:16:40.957Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:16:41.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:16:41.093Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:16:41.722Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21836,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:16:41.777Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:17:11.209Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:17:11.210Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:17:11.210Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:17:11.210Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:17:11.210Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:17:11.210Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:17:12.127Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:17:12.149Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:17:12.282Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:17:12.288Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:17:12.982Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21916,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:17:13.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:18:50.175Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:18:50.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:18:50.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:18:50.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:18:50.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:18:50.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:18:51.043Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:18:51.064Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:18:51.199Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:18:51.204Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:18:51.827Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":9392,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:18:51.883Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:26:11.283Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:26:11.284Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:26:11.284Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:26:11.284Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:26:11.284Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:26:11.284Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:26:12.233Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:26:12.253Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:26:12.374Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:26:12.380Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:26:13.077Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4724,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:26:13.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:26:48.664Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:26:48.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:26:48.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:26:48.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:26:48.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:26:48.665Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:26:49.594Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:26:49.614Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:26:49.742Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:26:49.747Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:26:50.372Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23460,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:26:50.426Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:27:44.052Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:27:44.053Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:27:44.053Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:27:44.053Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:27:44.053Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:27:44.053Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:27:45.029Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:27:45.052Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:27:45.269Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:27:45.274Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:27:45.913Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5020,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:27:45.979Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:27:49.705Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:27:49.706Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:27:49.706Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:27:49.706Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:27:49.706Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:27:49.706Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:27:50.599Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:27:50.619Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:27:50.736Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:27:50.742Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:27:51.362Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25396,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:27:51.416Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:28:50.111Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:28:50.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:28:50.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:28:50.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:28:50.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:28:50.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:28:51.009Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:28:51.030Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:28:51.169Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:28:51.175Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:28:51.810Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":3968,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:28:51.863Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:31:14.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:31:14.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:31:14.176Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:31:14.177Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:31:14.177Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:31:14.177Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:31:15.217Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:31:15.279Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:31:15.429Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:31:15.435Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:31:16.088Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22916,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:31:16.143Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:31:52.364Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:31:52.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:31:52.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:31:52.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:31:52.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:31:52.365Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:31:53.258Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:31:53.279Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:31:53.385Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:31:53.392Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:31:54.026Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":7580,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:31:54.081Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:38:54.902Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:38:54.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:38:54.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:38:54.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:38:54.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:38:54.903Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:38:55.824Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:38:55.847Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:38:56.003Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:38:56.012Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:38:56.672Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":4564,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:38:56.729Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:53:04.245Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:53:04.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:53:04.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:53:04.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:53:04.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:53:04.246Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:53:05.122Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:53:05.143Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:53:05.274Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:53:05.279Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:53:05.940Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22984,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:53:05.995Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:53:11.756Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:53:11.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:53:11.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:53:11.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:53:11.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:53:11.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:53:12.596Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:53:12.618Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:53:12.762Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:53:12.768Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:53:13.425Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21208,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:53:13.479Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:54:33.141Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T14:54:33.142Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T14:54:33.142Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T14:54:33.142Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T14:54:33.142Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T14:54:33.142Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T14:54:34.092Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T14:54:34.111Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T14:54:34.266Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T14:54:34.274Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T14:54:35.015Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T14:54:35.070Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:10:58.131Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:10:58.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:10:58.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:10:58.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:10:58.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:10:58.132Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:10:59.065Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:10:59.086Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:10:59.221Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:10:59.226Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:10:59.858Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23584,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:10:59.914Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:12:12.878Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:12:12.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:12:12.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:12:12.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:12:12.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:12:12.879Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:12:13.733Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:12:13.754Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:12:13.877Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:12:13.883Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:12:14.565Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23596,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:12:14.618Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:13:13.515Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:13:13.515Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:13:13.516Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:13:13.516Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:13:13.516Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:13:13.516Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:13:14.478Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:13:14.502Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:13:14.657Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:13:14.663Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:13:15.358Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":8008,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:13:15.439Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:15:23.286Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:15:23.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:15:23.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:15:23.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:15:23.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:15:23.287Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:15:24.205Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:15:24.225Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:15:24.361Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:15:24.367Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:15:25.039Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":25692,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:15:25.093Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:21:02.195Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:21:02.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:21:02.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:21:02.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:21:02.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:21:02.196Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:21:03.122Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:21:03.145Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:21:03.266Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:21:03.272Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:21:03.908Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:21:03.973Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":23540,"level":60,"path":"/v1/server/downloader/steam","method":"POST","server":"9da924ad-99a6-4b8e-b6ac-7ae13c30b5f2","err":{"message":"_ is not defined","name":"ReferenceError","stack":"ReferenceError: _ is not defined\n    at C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\GitSteamController.js:80:109\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\steamDownloader\\install.js:69:17)\n    at ChildProcess.emit (node:events:539:35)\n    at ChildProcess.emit (node:domain:537:15)\n    at maybeClose (node:internal/child_process:1092:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:302:5)\n    at Process.callbackTrampoline (node:internal/async_hooks:130:17)"},"msg":"_ is not defined","time":"2022-08-18T15:21:45.510Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:22:05.308Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:22:05.309Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:22:05.309Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:22:05.309Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:22:05.309Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:22:05.309Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:22:06.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:22:06.208Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:22:06.312Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:22:06.317Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:22:07.030Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20852,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:22:07.102Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:22:55.729Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:22:55.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:22:55.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:22:55.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:22:55.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:22:55.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:22:56.606Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:22:56.629Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:22:56.750Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:22:56.756Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:22:57.400Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":22272,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:22:57.456Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:23:37.333Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:23:37.334Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:23:37.334Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:23:37.334Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:23:37.334Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:23:37.334Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:23:38.167Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:23:38.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:23:38.443Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:23:38.449Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:23:39.110Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20216,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:23:39.166Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:25:39.582Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:25:39.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:25:39.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:25:39.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:25:39.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:25:39.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:25:40.436Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:25:40.456Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:25:40.578Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:25:40.584Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:25:41.220Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":6640,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:25:41.275Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:29:56.730Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:29:56.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:29:56.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:29:56.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:29:56.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:29:56.731Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:29:57.586Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:29:57.608Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:29:57.757Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:29:57.764Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:29:58.399Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24976,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:29:58.461Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:35:55.173Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:35:55.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:35:55.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:35:55.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:35:55.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:35:55.174Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:35:56.056Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:35:56.077Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:35:56.219Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:35:56.225Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:35:56.885Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":19788,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:35:56.942Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:36:52.788Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-18T15:36:52.788Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-18T15:36:52.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-18T15:36:52.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-18T15:36:52.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-18T15:36:52.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-18T15:36:53.617Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-18T15:36:53.638Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-18T15:36:53.766Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-18T15:36:53.771Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-18T15:36:54.385Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":21936,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-18T15:36:54.438Z","v":0}
