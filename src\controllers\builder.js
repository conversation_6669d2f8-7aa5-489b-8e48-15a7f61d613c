'use strict';
const rfr = require('rfr');
const Async = require('async');
const _ = require('lodash');
const Fs = require('fs-extra');
const Path = require('path');

const Log = rfr('src/helpers/logger.js');
const ConfigHelper = rfr('src/helpers/config.js');
const InitializeHelper = rfr('src/helpers/initialize.js').Initialize;
const DeleteController = rfr('src/controllers/delete.js');
const SteamCMD = require('../steamDownloader/steamcmd');

const Initialize = new InitializeHelper();
const Config = new ConfigHelper();

class Builder {
    constructor(json) {
        if (!json || !_.isObject(json) || json === null || !_.keys(json).length) {
            throw new Error('Invalid JSON was passed to Builder.');
        }
        this.json = json;
        this.log = Log.child({ server: this.json.uuid });
    }

    init(next) {

        const basePath = Config.get('sftp.path', '/srv/daemon-data')
        const installDir = Path.join(basePath, this.json.uuid)
        const envs = this.json.build.env


        Async.auto({
            create_folder: callback => {
                Fs.ensureDir(installDir, callback);
            },

            download_game_files: ['create_folder', (results, callback) => {

                const options = {}
                options.username = _.get(envs, 'STEAM_USERNAME', 'anonymous');
                options.password = _.get(envs, 'STEAM_PASSWORD');
                options.applicationId = _.get(envs, 'STEAM_APP_ID')
                options.path = installDir

                SteamCMD.install(options, callback)
            }],

            initialize: ['download_game_files', (results, callback) => {
                Initialize.setup(this.json, callback);
            }],
            block_boot: ['initialize', (results, callback) => {
                results.initialize.blockStartup(true, callback);
            }],

            run_scripts: ['block_boot', (results, callback) => {
                results.initialize.option.install(callback);
            }],
            unblock_boot: ['run_scripts', (results, callback) => {
                results.initialize.blockStartup(false, callback);
            }],
        }, err => {
            next(err, this.json);

            // Delete the server if there was an error causing this builder to abort.
            if (err) {
                const Delete = new DeleteController(this.json);
                Delete.delete(deleteError => {
                    if (deleteError) Log.error(deleteError);
                });
            }
        });
    }
}

module.exports = Builder;
