{"name": "resitfy-win-daemon", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node src/index.js | node_modules/bunyan/bin/bunyan -o short", "test": "./node_modules/eslint/bin/eslint.js --quiet --config .eslintrc src scripts", "configure": "node scripts/configure.js", "diagnostics": "node scripts/diagnostics.js", "migrate": "node scripts/migrate.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"ansi-escape-sequences": "^4.0.1", "async": "^2.6.1", "bunyan": "^1.8.15", "carrier": "^0.3.0", "chokidar": "^3.5.3", "compare-versions": "^4.1.3", "compressing": "^1.5.1", "detect-file-encoding-and-language": "^2.1.0", "eval": "^0.1.6", "extendify": "^1.0.0", "fs-extra": "^10.1.0", "ftp-srv-u": "^1.1.0", "get-folder-size": "^2.0.1", "getos": "^3.2.1", "iconv-lite": "^0.6.3", "ini": "^1.3.8", "inquirer": "^9.0.2", "isstream": "^0.1.2", "js-yaml": "^4.1.0", "jsdom": "^20.0.0", "keypair": "^1.0.4", "klaw": "^4.0.1", "lodash": "^4.17.21", "memory-cache": "^0.2.0", "mime": "^3.0.0", "mime-kind": "^3.0.0", "moment": "^2.29.4", "node-yaml": "^4.0.1", "path-extra": "^3.0.0", "properties-parser": "^0.3.1", "randomstring": "^1.2.2", "request": "^2.88.2", "restify": "^4.3.4", "rimraf": "^3.0.2", "restify-cors-middleware": "^1.1.1", "rfr": "^1.2.3", "simple-git": "^3.7.1", "socket.io": "^2.0.4", "socketio-file-upload": "^0.7.3", "steam-workshop": "0.0.5", "tail": "^2.2.4", "pidusage": "^3.0.0", "ps-tree": "^1.2.0", "systeminformation": "^4.26.9", "tarball-extract": "0.0.6", "taskcc-nssm": "^0.1.1", "unzipper": "^0.10.10", "windows-service-wrapper": "^1.0.2", "xml2js": "^0.4.23"}}