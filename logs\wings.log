{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:02:43.745Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T06:02:43.746Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T06:02:43.746Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"|  Copyright 2015 - 2019 <PERSON>  |","time":"2022-08-20T06:02:43.746Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:02:43.747Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T06:02:43.747Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T06:02:45.112Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T06:02:45.135Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T06:02:45.922Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T06:02:45.930Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T06:02:46.590Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2184,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T06:02:46.670Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:11:51.186Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T06:11:51.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T06:11:51.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T06:11:51.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:11:51.187Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T06:11:51.188Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T06:11:52.205Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T06:11:52.229Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T06:11:52.338Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T06:11:52.345Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T06:11:52.996Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":5428,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T06:11:53.066Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:25:14.743Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T06:25:14.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T06:25:14.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T06:25:14.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:25:14.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T06:25:14.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T06:25:15.583Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T06:25:15.604Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T06:25:15.713Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T06:25:15.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T06:25:16.382Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":18604,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T06:25:16.437Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:26:13.932Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T06:26:13.932Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T06:26:13.933Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T06:26:13.933Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T06:26:13.933Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T06:26:13.933Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T06:26:14.995Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T06:26:15.020Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T06:26:15.155Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T06:26:15.162Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T06:26:15.810Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":20196,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T06:26:15.871Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:56:08.076Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T07:56:08.077Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T07:56:08.077Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T07:56:08.078Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:56:08.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T07:56:08.079Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":10916,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T07:56:08.860Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:56:15.036Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T07:56:15.037Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T07:56:15.038Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T07:56:15.039Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:56:15.039Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T07:56:15.039Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T07:56:15.841Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T07:56:15.861Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T07:56:15.995Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T07:56:16.001Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T07:56:16.634Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":24572,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T07:56:16.687Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:58:59.787Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"| Running Pterodactyl Daemon v1.0.0    |","time":"2022-08-20T07:58:59.788Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"|        https://pterodactyl.io        |","time":"2022-08-20T07:58:59.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"|  Copyright 2015 - 2019 Dane Everitt  |","time":"2022-08-20T07:58:59.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"+ ------------------------------------ +","time":"2022-08-20T07:58:59.789Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Loading modules, this could take a few seconds.","time":"2022-08-20T07:58:59.790Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Modules loaded, starting Pterodactyl Daemon...","time":"2022-08-20T07:59:00.561Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Contacting panel to retrieve a list of currrent Eggs available to the node.","time":"2022-08-20T07:59:00.581Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Checking existing eggs against Panel response...","time":"2022-08-20T07:59:00.719Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Beginning server initialization process.","time":"2022-08-20T07:59:00.725Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Configuring websocket for daemon stats...","time":"2022-08-20T07:59:01.328Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Pterodactyl Daemon is now listening for insecure connections on 0.0.0.0:8080","time":"2022-08-20T07:59:01.378Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"server":"81400da7-60f6-4b3b-9a35-8dd419fa5251","level":40,"msg":"Server booting is now BLOCKED.","time":"2022-08-20T08:07:04.578Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"server":"81400da7-60f6-4b3b-9a35-8dd419fa5251","level":30,"msg":"Blocking server boot until option installation process is completed.","time":"2022-08-20T08:07:04.578Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":60,"err":{"message":"Recieved a non-200 error code when attempting to check scripts for server.","name":"Error","stack":"Error: Recieved a non-200 error code when attempting to check scripts for server.\n    at Request._callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\src\\controllers\\option.js:41:31)\n    at Request.self.callback (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\request\\request.js:185:22)\n    at Request.emit (node:events:527:28)\n    at Request.emit (node:domain:537:15)\n    at Request.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\request\\request.js:1154:10)\n    at Request.emit (node:events:527:28)\n    at Request.emit (node:domain:537:15)\n    at IncomingMessage.<anonymous> (C:\\Users\\<USER>\\WebstormProjects\\resitfy-win-daemon\\node_modules\\request\\request.js:1076:12)\n    at Object.onceWrapper (node:events:641:28)\n    at IncomingMessage.emit (node:events:539:35)\n    at IncomingMessage.emit (node:domain:537:15)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)"},"meta":{"code":500,"requestUrl":"http://homestead.test/api/remote/scripts/31bcdd48-6f84-4ed9-88ab-365e94692267"},"msg":"A fatal error was encountered while attempting to create a server.","time":"2022-08-20T08:07:04.744Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"level":30,"msg":"Notified remote panel of server install status.","time":"2022-08-20T08:07:04.851Z","v":0}
{"name":"wings","hostname":"DESKTOP-7FV9LHE","pid":2284,"server":"81400da7-60f6-4b3b-9a35-8dd419fa5251","level":30,"msg":"Server deleted.","time":"2022-08-20T08:07:36.031Z","v":0}
