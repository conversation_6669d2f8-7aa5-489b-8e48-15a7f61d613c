'use strict';

/**
 * Pterodactyl - Daemon
 * Copyright (c) 2015 - 2018 <PERSON> <<EMAIL>>.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
const rfr = require('rfr');
const isStream = require('isstream');
const Async = require('async');
const Util = require('util');
const _ = require('lodash');
const Carrier = require('carrier');
const Fs = require('fs-extra');
const Ansi = require('ansi-escape-sequences');
const Moment = require('moment');
const Path = require("path");
const si = require("systeminformation");
const Tail = require('tail').Tail;
const Nssm = require('windows-service-wrapper').Service;

const Log = rfr('src/helpers/logger.js');
const Status = rfr('src/helpers/status.js');
const LoadConfig = rfr('src/helpers/config.js');

const Config = new LoadConfig();


class Service {
    constructor(server, next) {
        this.server = server;
        this.containerID = _.get(this.server.json, 'uuid', null);
        this.container = new Nssm()

        this.stream = undefined;
        this.procData = undefined;
        this.logStream = null;


        const location = _.get(this.server.service.config, 'log.location')
        const basePath = Config.get('sftp.path', '/srv/daemon-data')


        this.logPath = Path.join(basePath, this.containerID, location)
        // Check status and attach if server is running currently.
        this.reattach().then(running => {
            next(null, running);
        }).catch(next);

    }


    /**
     * Reattach to a running docker container and reconfigure the log streams. Returns a promise value of true
     * if the container is actually running, or false if the container is stopped.
     *
     * @return {Promise<any>}
     */
    reattach() {
        return new Promise((resolve, reject) => {
            si.services(this.containerID).then(res => {
                const service = res[0]
                if (service.running) {
                    this.server.setStatus(Status.ON);

                    Promise.all([
                        this.readLogStream(),
                    ]).then(() => {
                        resolve(true);
                    }).catch(reject);

                } else {
                    resolve()

                }


            }).catch(e => reject)

        });
    }


    truncateLogs(path) {
        return new Promise((resolve, reject) => {
            Fs.ensureFile(path, err => {
                if (err) {
                    reject(err);
                }

                Fs.truncate(path, 0, truncateError => {
                    if (truncateError) {
                        return reject(truncateError);
                    }

                    resolve();
                });
            });
        });
    }


    /**
     * Starts a given container and returns a callback when finished.
     * @param  {Function} next [description]
     * @return {[type]}        [description]
     */
    start(next) {

        this.container.start(this.containerID).then(() => {


            this.truncateLogs(this.logPath).then(() => {
                this.server.setStatus(Status.STARTING);
                Promise.all([
                    this.readLogStream(),
                ]).then(() => {
                    next();
                }).catch(next);
            }).catch(next);
        }).catch(err => {
            if (err && _.includes(err.message, 'container already started')) {
                this.server.setStatus(Status.ON);
                return next();
            }

            next(err);
        });
    }


    /**
     * Stops a given container and returns a callback when finished.
     * @param  {Function} next
     * @return {Function}
     */
    stop(next) {
        this.container.stop(this.containerID).then(() => next()).catch(e => next());
    }

    update(next) {

        this.container.set(this.containerID, {
            AppParameters: this.server.json.build.env.STARTUP,
        }).then(() => next()).catch(e => next());


    }

    /**
     * Kills a given container and returns a callback when finished.
     * @param  {Function} next [description]
     * @return {[type]}        [description]
     */
    kill(next) {
        this.container.stop(this.containerID).then(res => next()).catch(e => next());
    }


    /**
     * Rather than attaching to a container and reading the stream output, connect to the container's
     * logs and use those as the console output. The container will still be attached to in order to
     * handle sending data and monitoring for crashes.
     *
     * However, because attaching takes some time, often we lose important error messages that can help
     * a user to understand why their server is crashing. By reading the logs we can avoid this problem
     * and get them all of the important context.
     *
     * @return {Promise<void>}
     */
    readLogStream() {
        return new Promise((resolve, reject) => {
            if (!_.isNull(this.logStream)) {
                this.logStream.unwatch();
                this.logStream = null;
            }

            Fs.ensureFile(this.logPath).then(() => {
                this.logStream = new Tail(this.logPath);

                this.logStream.on('line', data => {
                    // const j = JSON.parse(data.toString());
                    this.server.output(_.trim(data));
                });

                this.logStream.on('error', err => {
                    this.server.log.error(err);
                });

                resolve();
            }).catch(reject);

        });
    }

    /**
     * Reads the last 'n' bytes of the server's log file.
     *
     * @param {Number} bytes
     * @return {Promise<any>}
     */
    readEndOfLog(bytes) {
        return new Promise((resolve, reject) => {
            // When a container is first created the log path is surprisingly not set. Because of this things
            // will die when attempting to setup the tail. To avoid this, set the path manually if there is no
            // path set.


            Fs.stat(this.logPath, (err, stat) => {
                if (err && err.code === 'ENOENT') {
                    return resolve('');
                } else if (err) {
                    return reject(err);
                }

                let opts = {};
                let lines = '';
                if (stat.size > bytes) {
                    opts = {
                        start: (stat.size - bytes),
                        end: stat.size,
                    };
                }

                const ReadStream = Fs.createReadStream(this.logPath, opts);

                ReadStream.on('data', data => {
                    _.forEach(_.split(data.toString(), /\r?\n/), line => {
                        try {
                            const j = JSON.parse(line);
                            lines += j.log;
                        } catch (e) {
                            // do nothing, JSON parse error because we caught the tail end of a line.
                        }
                    });
                });

                ReadStream.on('end', () => {
                    resolve(lines);
                });

            });
        })
    }


    destroy(container, next) {
        const FindContainer = DockerController.getContainer(container);
        FindContainer.inspect(err => {
            if (!_.isNull(this.logStream)) {
                this.logStream.unwatch();
                this.logStream = null;
            }

            if (!err) {
                this.container.remove(next);
            } else if (err && _.startsWith(err.reason, 'no such container')) { // no such container
                this.server.log.debug({container_id: container}, 'Attempting to remove a container that does not exist, continuing without error.');
                return next();
            } else {
                return next(err);
            }
        });
    }
}

module.exports = Service;
